import { ObjectId } from 'bson';
import UniversalTrackerValue, {
  type SourceItem,
  type UniversalTrackerValuePlain,
  type ValueHistory
} from '../../../models/universalTrackerValue';
import { DataPeriods } from '../constants';
import { ValueAggregation } from "../../../models/public/universalTrackerType";
import { getAggregatorByUniversalTrackerFn } from "./utrTypeAggregator";
import { AggregationMode } from "../../../models/public/universalTrackerType";
import UniversalTracker, { UniversalTrackerPlain } from '../../../models/universalTracker';
import { wwgLogger } from "../../wwgLogger";

type UtrvPick = Pick<UniversalTrackerValuePlain, | 'initiativeId' | 'period' | 'status' | 'effectiveDate'>;
type HistoryPick = Pick<ValueHistory, 'value' | 'valueData' | 'action' | 'date' | 'unit' | 'numberScale'>;

interface HistoryData extends HistoryPick, UtrvPick {
  utrvId: ObjectId;
  surveyId?: ObjectId;
  latestHistory?: HistoryPick;
}

export class AggregatedUtrvManager {

  constructor(private readonly logger: typeof wwgLogger) {
  }

  public async getDisaggregation(sourceItems: SourceItem[]) {

    if (sourceItems.length === 0) {
      return [];
    }

    const disaggregatedUtrvs = await UniversalTrackerValue.find({
      _id: { $in: sourceItems.map((item) => item.utrvId) },
    })
      .lean()
      .exec();

    const utrvHistoryMap = new Map(sourceItems.map((item) => [item.utrvId.toString(), item]));

    return disaggregatedUtrvs.reduce<HistoryData[]>((acc, utrv) => {
      const item = utrvHistoryMap.get(utrv._id.toString());
      const history = item?.historyId ? utrv.history.find((h) => h._id?.equals(item.historyId)) : undefined;
      if (!history) {
        return acc;
      }

      const latestHistory = item?.latestHistoryId ?
        utrv.history.find((h) => h._id?.equals(item.latestHistoryId)) :
        undefined;

      const historyItem: HistoryData = {
        utrvId: utrv._id as ObjectId,
        surveyId: utrv.compositeData?.surveyId,
        initiativeId: utrv.initiativeId,
        effectiveDate: utrv.effectiveDate,
        period: utrv.period || DataPeriods.Yearly,
        date: history.date,
        value: history.value,
        valueData: history.valueData,
        action: history.action,
        status: utrv.status,
        unit: history.unit,
        numberScale: history.numberScale,
        latestHistory: latestHistory ? {
          value: latestHistory.value,
          valueData: latestHistory.valueData,
          action: latestHistory.action,
          date: latestHistory.date,
          unit: latestHistory.unit,
          numberScale: latestHistory.numberScale,
        } : undefined,
      }
      acc.push(historyItem);
      return acc;
    }, []);
  }

  async getValueAggregation(
    utrv: Pick<UniversalTrackerValuePlain, '_id' | 'valueAggregation' | 'universalTrackerId' | 'valueType' | 'initiativeId'>,
    historyData: Pick<HistoryData, 'initiativeId' | 'utrvId'>[],
  ): Promise<ValueAggregation> {

    if (utrv.valueAggregation) {
      return utrv.valueAggregation;
    }

    const id = utrv.initiativeId.toString();
    const isAllSame = historyData.every((item) => item.initiativeId.toString() === id);
    // If all history items have the same initiativeId, we assume it should be "combined" from the same initiative,
    // however, this is the best guess, and we should always have aggregated utrv containing valueAggregation
    const mode = isAllSame ? AggregationMode.Combined : AggregationMode.Children;

    this.logger.warn('getValueAggregation: No value aggregation found, trying to figure out default', {
      utrvId: utrv._id,
      initiativeId: id,
      mode,
      historyData: historyData.map((item) => ({
        initiativeId: item.initiativeId.toString(),
        utrvId: item.utrvId.toString(),
      })),
    });

    if (utrv.valueType) {
      return getAggregatorByUniversalTrackerFn({ valueType: utrv.valueType }, mode);
    }

    // Load utr and get the aggregator
    const universalTracker = await UniversalTracker.findById(utrv.universalTrackerId, {
      valueType: 1,
      valueAggregation: 1,
    }).lean<Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation'>>().orFail().exec();

    return getAggregatorByUniversalTrackerFn(universalTracker, mode);
  }
}

let instance: AggregatedUtrvManager;
export const getAggregatedUtrvManager = () => {
  if (!instance) {
    instance = new AggregatedUtrvManager(wwgLogger);
  }
  return instance;
};
