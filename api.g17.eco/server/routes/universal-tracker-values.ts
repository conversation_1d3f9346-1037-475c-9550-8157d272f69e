/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import express from 'express';
import UniversalTrackerValue, { UniversalTrackerValuePlain } from '../models/universalTrackerValue';
import FileUpload from '../http/FileUpload';
import cache, { clearCache } from '../service/cache';
import { getUniversalTrackerValueManager } from '../service/utr/UniversalTrackerValueManager';
import { UniversalTrackerRepository } from '../repository/UniversalTrackerRepository';
import { ObjectId } from 'bson';
import { createCompositeUniversalTrackerValueManager } from '../service/utr/CompositeUniversalTrackerValueManager';
import { getOnboardingManager, StakeholderTypes, } from '../service/onboarding/OnboardingManager';
import { ActionMap, DataPeriods, UtrvType } from '../types/constants';
import { checkStakeholderType, populateInitiative } from '../middleware/commonMiddlewares';
import UniversalTrackerActionRepository from '../repository/UniversalTrackerActionRepository';
import { DelegationPermissions } from '../service/delegation/DelegationPermissions';
import { fromHttpRequest, fromRequest } from '../service/utr/model/actionRequest';
import { createUniversalTrackerValueBundler } from '../service/utr/UniversalTrackerValueBundler';
import { combinedEvidence } from '../service/utr/utrvHistory';
import documentModel from '../models/document';
import { addDocumentUrl } from '../service/storage/fileStorage';
import { UniversalTrackerValueRepository } from '../repository/UniversalTrackerValueRepository';
import { getCreateManager } from '../service/utr/CreateManager';
import { UtrvPermissions } from '../service/utr/UtrvPermissions';
import { ProcessUtrvUpdateResult } from '../rules/rule';
import { UniversaleTrackerValueValidator } from '../service/ledger/UniversalTrackerValueValidator';
import { Qldb } from '../service/ledger/Qldb';
import { Ethereum } from '../service/ledger/Ethereum';
import UniversalTrackerValueService from '../service/utr/UniversalTrackerValueService';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { UserModel } from '../models/user';
import { getNotificationManager } from '../service/notification/NotificationManager';
import { wwgLogger } from '../service/wwgLogger';
import { Actions } from '../service/action/Actions';
import { UtrvAccessFilter } from '../service/utr/UtrvAccessFilter';
import { ContextMiddleware } from '../middleware/audit/contextMiddleware';
import UniversalTracker, { ConnectionUtr, UniversalTrackerPlain } from '../models/universalTracker';
import { ensureMap } from '../service/utr/CalculationUniversalTrackerService';
import { connectUtrFields } from '../repository/projections';
import { UserRepository } from '../repository/UserRepository';
import { AssuranceRepository } from '../repository/AssuranceRepository';
import { AssurancePortfolioPlain } from '../service/assurance/model/AssurancePortfolio';
import UniversalTrackerActionManager from '../service/utr/UniversalTrackerActionManager';
import { getRootInitiativeService } from '../service/organization/RootInitiativeService';
import { getAuditLogger } from '../service/audit/AuditLogger';
import { UtrvAudit } from '../service/audit/events/utrv';
import { LEVEL } from '../service/event/Events';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';
import {
  type AccessUtrv,
  canAccessUtrv,
  canContributeUtrv,
  canManageTargetBaseline,
  canRejectUtrv,
  canVerifyUtrv
} from '../middleware/utrvMiddleware';
import { AuthRouter } from '../http/AuthRouter';
import UserError from '../error/UserError';
import DOMPurify from "isomorphic-dompurify";
import { getAggregatedUtrvManager } from '../service/utr/aggregation/AggregatedUtrvManager';
import { TargetBaselineUpdate, getTargetBaselineManager } from '../service/utr/TargetBaselineManager';
import { getLedgerService } from '../service/ledger/microservice/LedgerService';
import ContextError from '../error/ContextError';
import { InitiativePlain } from '../models/initiative';
import { SurveyModelPlain } from '../models/survey';
import { getVariationService } from '../service/initiative-universal-tracker/VariationService';
import { AggregatedUniversalTracker } from '../service/utr/aggregation/AggregatedUniversalTracker';
import { AggregationMode } from '../models/public/universalTrackerType';
import { mustValidate } from '../util/validation';
import { tableDataSchema } from '../routes/validation-schemas/universal-tracker-value';
import { getIntegrationManager } from '../service/integration/IntegrationManager';
import { z } from "zod";
import { checkIsSuperAdmin } from "../middleware/userMiddlewares";
import { getAggregationPreviewManager } from '../service/utr/aggregation/AggregationPreviewManager';
import { AggregationErrorMessages } from '../error/ErrorMessages';

const utrvManager = getUniversalTrackerValueManager();
const compositeUtrvManager = createCompositeUniversalTrackerValueManager();
const onboardingManager = getOnboardingManager();
const bundler = createUniversalTrackerValueBundler();
const delegationPermissions = new DelegationPermissions();
const router = express.Router() as AuthRouter;
const createManager = getCreateManager();
const aggregatedUtrvManager = getAggregatedUtrvManager();
const targetBaselineManager = getTargetBaselineManager();
const integrationManager = getIntegrationManager();

// @TODO - does this need FileUpload? Doesn't seem to do anything with it?
router.route('/')
  .post(FileUpload.any(), ContextMiddleware, async (req, res) => {
    if (req.body._id) {
      return res.Invalid('Not allowed to specify _id on create');
    }
    try {
      const upsertRequest = fromRequest(req);
      const hasPerm = await InitiativePermissions.canManageInitiative(
        req.user,
        upsertRequest.data.initiativeId
      );
      if (!hasPerm) {
        return res.NotPermitted();
      }

      const result = await createManager.upsertFromData(upsertRequest);
      clearCache();
      return res.FromModel(result);
    } catch (e) {
      return res.Exception(e);
    }
  });

router.route('/:utrvId')
  .get(async (req, res) => {
    try {
      const [utrv] = await UniversalTrackerRepository.getHistoryInfoForIds(
        [req.params.utrvId],
        { utr: true }
      );

      if (!utrv || (!await UtrvPermissions.canAccess(utrv, req.user) && !await UtrvPermissions.canAssure(utrv, req.user))) {
        return res.NotPermitted();
      }
      const providers = await integrationManager.allProviders();
      return res.FromModel({ ...utrv, providers });
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/:utrvId/proof')
  .get(async (req, res) => {
    try {
      const ledgerService = getLedgerService();
      if (ledgerService.isEnabled()) {
        const response = await ledgerService.verifyUTRVHistory(req.params.utrvId);
        return res.FromModel(response);
      }
      res.Exception(new ContextError('Ledger service is not enabled'));
    }
    catch (e) {
      return res.Exception(e);
    }

    try {
      const qldbClient = Qldb.getClient();
      const qldbDriver = Qldb.getDriver();

      if (!qldbDriver || !qldbClient) {
        return res.Invalid();
      }

      const qldbService = new Qldb(qldbClient, qldbDriver);
      const ethereumService = new Ethereum();
      const validator = new UniversaleTrackerValueValidator(qldbService, ethereumService);
      const response = await validator.getStatus(req.params.utrvId);
      res.FromModel(response);
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/:utrvId/comments')
  .get(async (req, res) => {
    try {
      const utrv = await UniversalTrackerValue.findById(req.params.utrvId).lean<UniversalTrackerValuePlain>();
      if (!utrv || !await UtrvPermissions.canAccessComments(utrv, req.user)) {
        return res.NotPermitted();
      }

      const assurancePortfolios: Pick<AssurancePortfolioPlain, 'organizationId'>[] | null
        = await AssuranceRepository.findActivePortfoliosByUtrvId(utrv._id);
      const assurancePortfoliosUsers = assurancePortfolios ?
        await AssuranceRepository.getUsersByArray(assurancePortfolios, { firstName: 1, surname: 1 })
        : [];

      const [
        comments,
        initiativeTreeUsers,
      ] = await Promise.all([
        await utrvManager.getComments(utrv),
        await UserRepository.getUsersByInitiativeTree(String(utrv.initiativeId)),
      ]);

      const allUsers: Map<string, { _id: ObjectId, firstName: string, surname: string, profile?: string; }> = new Map();

      comments?.users.forEach(u => allUsers.set(String(u._id), u));
      initiativeTreeUsers.forEach(u => allUsers.set(String(u._id), u));
      assurancePortfoliosUsers.map(a => ({ ...a, surname: `${a.surname} (Assurer)` })).forEach(u => allUsers.set(String(u._id), u));

      const users = [...allUsers.values()]
        .sort((a, b) => {
          const aa = `${a.surname}, ${a.firstName}`;
          const bb = `${b.surname}, ${b.firstName}`;
          return aa < bb ? 1 : aa > bb ? -1 : 0;
        })

      return res.FromModel({
        items: comments.items ?? [],
        users
      });
    } catch (e) {
      res.Exception(e);
    }
  })
  .patch(ContextMiddleware, async (req, res, next) => {
    try {
      const utrv = await UniversalTrackerValue.findById(req.params.utrvId).populate('universalTracker').orFail().exec();
      const hasUtrvAccess = await UtrvPermissions.canAccess(utrv, req.user);
      const hasAssurerAccess = !hasUtrvAccess ? await UtrvPermissions.canAssure(utrv, req.user) : false;

      if (!hasUtrvAccess && !hasAssurerAccess) {
        return res.NotPermitted();
      }

      const sanitizedText = DOMPurify.sanitize(req.body.text);
      const { commentId, mentionUserIds } = await utrvManager.addUtrvComment(utrv, sanitizedText, req.user);

      await getNotificationManager().sendCommentMentionNotification({
        utrv,
        user: req.user,
        commentId,
        recipientIds: mentionUserIds,
      });

      await getNotificationManager().sendCommentNotification({
        utrv,
        user: req.user,
        text: sanitizedText,
        commentId,
        filteredUserIds: mentionUserIds
      });

      return res.Success();
    } catch (e) {
      next(e);
    }
  })

router.route('/:utrvId/comments/:commentId')
  .delete(async (req, res) => {
    try {
      const utrv = await UniversalTrackerValue.findById(req.params.utrvId).lean<UniversalTrackerValuePlain>();
      if (!utrv || !await UtrvPermissions.canAccess(utrv, req.user)) {
        return res.NotPermitted();
      }

      await utrvManager.deleteComment(utrv, req.user, req.params.commentId);
      return res.Success();
    } catch (e) {
      res.Exception(e);
    }
  });


router.route([
  '/:utrvId/stakeholders',
  '/:utrvId/users',
])
  .get((req, res) => {
    UniversalTrackerValueRepository.mustFindById(req.params.utrvId).then(async utrv => {
      if (!await UtrvPermissions.canAccess(utrv, req.user)) {
        throw new PermissionDeniedError();
      }

      const users = await UniversalTrackerValueRepository.getUtrvStakeholderUsersByUtrv(utrv);
      const onboardingUsers = await onboardingManager.findOnboardingUtrvUsers([utrv], utrv.initiativeId);
      const permissions = await delegationPermissions.getUtrvPermissions(utrv, req.user);
      res.FromModel({ users: [...users, ...onboardingUsers], permissions });
    }).catch((e: Error) => res.Exception(e));
  });

router.route('/:utrvId/download')
  .get((req, res) => {
    bundler.download(req.params.utrvId, req.user)
      .then(bundles => res.FromModel(bundles))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:utrvId/stakeholders/:role/:email/onboard')
  .patch(checkStakeholderType, ContextMiddleware, (req, res) => {
    const { email, role, utrvId } = req.params;
    onboardingManager.onboardUtrv(email, role as StakeholderTypes, utrvId, req.user, req.body)
      .then((resp) => res.Success(resp))
      .catch((e: Error) => res.Exception(e));
  })
  .delete(checkStakeholderType, (req, res) => {
    const { email, role, utrvId } = req.params;
    onboardingManager.removeOnboardUtrv(email, role as StakeholderTypes, utrvId, req.user)
      .then((resp) => res.Success(resp))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:utrvId/stakeholders/:role/:userId')
  .patch(checkStakeholderType, ContextMiddleware, (req, res) => {
    const { userId, utrvId, role } = req.params;
    onboardingManager.userUtrvAction(userId, utrvId, role as StakeholderTypes, Actions.Add, req.user)
      .then((result) => res.FromModel(result))
      .catch((e: Error) => res.Exception(e));
  })
  .delete(checkStakeholderType, ContextMiddleware, (req, res) => {
    const { userId, utrvId, role } = req.params;
    onboardingManager.userUtrvAction(userId, utrvId, role as StakeholderTypes, Actions.Remove, req.user)
      .then((result) => res.FromModel(result))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:utrvId/modal/info')
  .get(async (req, res, next) => {
    try {
      const utrv = await UniversalTrackerValueRepository.mustFindById(req.params.utrvId);
      const hasUtrvAccess = await UtrvPermissions.canAccess(utrv, req.user);
      if (!hasUtrvAccess && !await UtrvPermissions.canAssure(utrv, req.user)) {
        return res.NotPermitted();
      }
      const data = await UniversalTrackerActionRepository.getUtrvByIdExtended(req.params.utrvId);
      res.FromModel(data)
    } catch (e) {
      next(e);
    }
  });

// Used by Survey QuestionContainer
router.route('/:utrvId/history')
  .get((req, res) => {
    UniversalTrackerActionRepository.getUtrvStakeholdersHistory(req.params.utrvId, req.user)
      .then(async (data) => {

        const { latestHistory } = data;
        const { stakeholderHistory, verifierHistory } = latestHistory;
        const evidence = combinedEvidence([stakeholderHistory, verifierHistory]);
        const documents = await documentModel.find({ _id: { $in: evidence } }).lean().exec();

        // Rebuild back into data structure
        return {
          ...data,
          latestHistory: await addDocumentUrl({ ...latestHistory, documents })
        };
      })
      .then(data => res.FromModel(data))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:utrvId/connections')
  .get(async (req, res, next) => {

    const defaultMinScore = 0.5;
    const queryScore = Number(req.query.minScore ?? defaultMinScore);
    const minScore = isNaN(queryScore) ? defaultMinScore : queryScore;

    try {
      const utrv = await UniversalTrackerValueRepository.mustFindById(req.params.utrvId);
      if (!await UtrvPermissions.canAccess(utrv, req.user)) {
        return res.NotPermitted();
      }

      const utr = await UniversalTracker
        .findById(utrv.universalTrackerId, { connections: 1, })
        .orFail().exec();

      if (!utr.connections || utr.connections.length === 0) {
        return res.FromModel({ connections: [], utrvs: [] })
      }

      // Get connection codes, that have score above minScore
      const utrCodes = utr.connections.reduce((acc, con) => {
        if (con.score < minScore) {
          return acc;
        }

        const configVariables = ensureMap(con.variables).values();
        for (const v of configVariables) {
          acc.push(v.code)
        }
        return acc
      }, [] as string[]);

      const connectionUtrs: ConnectionUtr[] = await UniversalTracker
        .find({ code: { $in: utrCodes } }, connectUtrFields)
        .orFail().exec();

      const utrMap = new Map(connectionUtrs.map(u => [String(u._id), u]));

      const utrvs = await UniversalTrackerRepository.getLatestUtrvs({
        type: utrv.type,
        initiativeId: utrv.initiativeId,
        period: utrv.period ?? DataPeriods.Yearly,
        utrIds: connectionUtrs.map(u => u._id)
      })

      const connData = utrvs.map(v => ({
        ...v,
        universalTracker: utrMap.get(String(v.universalTrackerId)),
      }));

      res.FromModel({ connections: utr.connections, utrvs: connData });
    } catch (e) {
      next(e)
    }
  });

// Used by Composite Value History modal
router.route('/history/info')
  .get((req, res) => {
    const ids = req.query.ids as string[];
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.Invalid('Expected array of ids');
    }
    UniversalTrackerRepository.getHistoryInfoForIds(ids)
      .then(async (utrvs) => {
        const data = await UtrvAccessFilter.verifyAccess(utrvs, req.user);
        const providers = (await integrationManager.allProviders()).map(({ code, name }) => ({ code, name }));
        // No data means everything was filter out.
        data.length === 0 ? res.NotPermitted() : res.FromModel({ data, providers });
      })
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:utrvId/variations').get(canAccessUtrv ,async (req, res, next) => {
  try {
    const utrvId = res.locals.utrv._id as ObjectId;
    const variationService = getVariationService();
    // return extended variations with extra details
    const utrvVariations = await variationService.getUtrvVariations(utrvId);
    res.FromModel(utrvVariations);
  }
  catch (e) {
    next(e);
  }
});

router.route('/:utrvId/history/update')
  .patch(ContextMiddleware, (req, res) => {
    const utrvId = req.params.utrvId;
    const history = req.body.data;
    const user = req.user

    utrvManager.getCurrentHistoryData(utrvId, history, user)
      .then((model: any) => res.FromModel(model))
      .catch((e: Error) => res.Exception(e));
  });

router.route('/initiativeId/:initiativeId/recursive{/:surveyId}')
  .get(populateInitiative, cache.route(), async function (req, res) {
    const initiativeDoc = req.initiative;

    if (!initiativeDoc) {
      return res.Invalid();
    }

    try {
      const { surveyId } = req.params;

      if (!await InitiativePermissions.canAccess(req.user, initiativeDoc._id)) {
        res.FromModel([]); // Return empty data
        return;
      }

      const utrService = new UniversalTrackerValueService(initiativeDoc, surveyId);
      res.FromModel(await utrService.get());

    } catch (e) {
      res.Exception(e);
    }
  });

['target', 'baseline'].forEach(type => {

router.route(`/universal-tracker/:utrId/initiativeId/:initiativeId/${type}/latest`)
  .get(async (req, res) => {

    const initiativeId = req.params.initiativeId;
    if (!ObjectId.isValid(initiativeId)) {
      return res.Invalid('Invalid id provided');
    }

    if (!await InitiativePermissions.canAccess(req.user as UserModel, initiativeId)) {
      throw new PermissionDeniedError()
    }

    const latestUtrv = await UniversalTrackerValue.findOne({
      universalTrackerId: new ObjectId(req.params.utrId),
      initiativeId: new ObjectId(initiativeId),
      type,
      deletedDate: { $exists: false },
    }, '-stakeholders -history')
      .sort({ effectiveDate: -1 })
      .limit(1)
      .lean()
      .exec();

    res.FromModel(latestUtrv);
  });
});

const handleSubmitResults = async (result: ProcessUtrvUpdateResult[]) => {
  const ids = result.map(item => item._id)
  const utrvs = await UniversalTrackerValue
    .find({ _id: { $in: ids } })
    .populate('universalTracker', { name: 1, valueLabel: 1, code: 1 }).exec();

  return Promise.all(result.map(async item => {
    // Find utrv with utr populated, add utr information
    return {
      ...item,
      universalTracker: utrvs.find(m => String(item._id) === String(m._id))?.universalTracker,
    }
  }));
};

router.route('/:utrvId/update')
  .patch(canContributeUtrv, FileUpload.any(), ContextMiddleware, (req, res) => {
    utrvManager.updateRecursively(fromHttpRequest(req))
      .then((r) => handleSubmitResults(r))
      .then((result) => {
        clearCache();
        res.FromModel(result);
      })
      .catch(e => res.Exception(e));
  });

router
  .route('/:utrvId/update-target-baseline')
  .patch(ContextMiddleware, canManageTargetBaseline, async (req, res, next) => {
    try {
      const updateRequest: TargetBaselineUpdate = {
        id: req.params.utrvId,
        user: req.user,
        data: {
          ...req.body,
          initiativeId: res.locals.initiativeId,
          editorState: req.body.editorState ? JSON.parse(req.body.editorState) : {},
        },
      };
      const result = await targetBaselineManager.processUpdate(updateRequest);
      clearCache();
      return res.FromModel(result);
    } catch (e) {
      return next(e);
    }
  });

router.route('/:utrvId/reject')
  .patch(canRejectUtrv, FileUpload.any(), ContextMiddleware, (req, res) => {
    utrvManager.reject(fromHttpRequest(req))
      .then(() => {
        clearCache();
        res.Success('Successfully updated universal tracker value with _id=' + req.params.utrvId);
      }).catch((e: Error) => res.Exception(e));
  });

router.route('/:utrvId/verify')
  .patch(canVerifyUtrv, FileUpload.any(), ContextMiddleware, (req, res) => {
    utrvManager.verify(fromHttpRequest(req))
      .then(() => {
        clearCache();
        res.Success('Successfully updated universal tracker value with _id=' + req.params.utrvId);
      })
      .catch((e: Error) => res.Exception(e));
  });

router.route('/:utrvId/assurance')
  .patch(canAccessUtrv, ContextMiddleware, async (req, res) => {
    try {
      const utrv = await UniversalTrackerValue.findById(req.params.utrvId)
        .populate<{ initiative: InitiativePlain }>('initiative')
        .populate<{ universalTracker: UniversalTrackerPlain }>('universalTracker', 'name code')
        .populate<{ survey: SurveyModelPlain }>('survey', { completedDate: 1 })
        .orFail()
        .exec();

      if (!utrv.initiative) {
        return res.Exception(new Error(`Failed to find initiative for utrv ${utrv._id}`));
      }

      const action = req.body.action;

      if (utrv.survey?.completedDate) {
        throw new UserError('Question cannot be locked/unlocked whilst survey is completed', {
          utrvId: utrv._id,
          surveyId: utrv.survey._id,
        });
      }

      const rootService = getRootInitiativeService();
      const rootOrg = await rootService.getOrganization(utrv.initiative);
      if (!await InitiativePermissions.canManageInitiative(req.user, rootOrg._id)) {
        const msg = action === 'open' ? 'unlock' : 'lock';
        return res.Exception(new PermissionDeniedError(`Only organisations admin can ${msg} assured question`));
      }

      const oldStatus = utrv.assuranceStatus;
      const result = await UniversalTrackerActionManager.changeAssuranceStatus(utrv, action);
      const auditLogger = getAuditLogger();

      // this match frontend button action, might need to change after
      const statusMsg = action === 'open' ? 'unlocked' : 'locked';

      auditLogger.fromContext({
        initiativeId: utrv.initiativeId,
        auditEvent: UtrvAudit.assuranceStatusChange,
        message: `Update question assurance status to ${statusMsg}`,
        severity: LEVEL.NOTICE,
        targets: [
          auditLogger.utrvTarget({
            _id: utrv._id,
            code: utrv.universalTracker?.code,
            name: utrv.universalTracker?.name,
          }),
          auditLogger.initiativeTarget(utrv.initiative),
        ],
        debugData: {
          oldStatus,
          newStatus: utrv.assuranceStatus,
          action
        }
      }).catch(wwgLogger.error)

      res.FromModel({ _id: result._id, assuranceStatus: result.assuranceStatus });
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/:utrvId/composite/:action')
  .patch(
    canAccessUtrv, // @TODO - this is too loose. Hopefully we can deprecate this soon!
    FileUpload.any(),
    ContextMiddleware,
    (req, res) => {
    const utrvId = req.params.utrvId;
    const { user, body, files } = req;
    compositeUtrvManager.updateComposite({
      id: utrvId,
      user,
      body,
      files,
      action: req.params.action as ActionMap,
      ip: req.ip,
      na: Boolean(req.body.valueData?.notApplicableType),
      autoVerify: Boolean(req.body.autoVerify),
    })
      .then((r) => handleSubmitResults(r))
      .then((result) => {
        clearCache();
        res.FromModel(result);
      })
      .catch(e => res.Exception(e));
  });

router.route('/:utrvId/disaggregation')
  .get(canAccessUtrv, async (_req, res) => {
    try {
      const utrv = res.locals.utrv as AccessUtrv;
      const disaggregatedData = await aggregatedUtrvManager.getDisaggregation(utrv.sourceItems ?? []);
      const valueAggregation = await aggregatedUtrvManager.getValueAggregation(utrv, disaggregatedData)

      return res.FromModel({ valueAggregation, disaggregatedData });
    } catch (e) {
      res.Exception(e);
    }
  });

router.route('/:utrvId/aggregated-table-data').post(canAccessUtrv, async (req, res, next) => {
  try {
    const { tableData } = mustValidate(req.body, tableDataSchema);
    const utrv = res.locals.utrv;
    const utr = await UniversalTracker.findById(utrv.universalTrackerId).orFail().lean<UniversalTrackerPlain>().exec();
    const aggregatedUtr = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
    aggregatedUtr.add({ ...utrv, valueData: { table: tableData } });
    return res.FromModel(aggregatedUtr.valueData?.table ?? []);
  } catch (e) {
    next(e);
  }
});

router.route('/:utrvId/aggregate/refresh').post(checkIsSuperAdmin, ContextMiddleware, canAccessUtrv,  async (req, res, next) => {
  // This does not update references, purely apply changes of existing data with their latest values
  const { action } = mustValidate(req.body, z.object({
    action: z.enum(['preview', 'update']).default('preview')
  }));

  const utrv = res.locals.utrv as AccessUtrv;
  if (!utrv.sourceItems?.length) {
    throw new UserError(
      AggregationErrorMessages.NoDisaggregatedData,
      { utrvId: utrv._id, sourceItems: utrv.sourceItems, type: utrv.type }
    );
  }

  const [utr, originalUtrv] = await Promise.all([
    UniversalTracker.findById(utrv.universalTrackerId).orFail().lean<UniversalTrackerPlain>().exec(),
    UniversalTrackerValue.findById(utrv._id).orFail().exec(),
  ]);

  const aggregationPreviewManager = getAggregationPreviewManager();
  if (action === 'preview') {
    return res.FromModel(await aggregationPreviewManager.previewAggregation({
      utr,
      originalUtrv,
    }));
  }

  if (action === 'update') {
    return res.FromModel(await aggregationPreviewManager.updateAggregation({
      utr,
      originalUtrv,
      userId: req.user._id,
    }));
  }

  throw new ContextError(`Unsupported action: ${action} for refreshing aggregated data. Only 'preview' and 'update' are supported.`);
});


module.exports = router;
