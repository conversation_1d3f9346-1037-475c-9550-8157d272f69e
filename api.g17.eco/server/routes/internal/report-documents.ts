import express from 'express';
import { getReportDocumentManager } from '../../service/report-document/ReportDocumentManager';

const router = express.Router();
const reportDocumentManager = getReportDocumentManager();

router.route('/:reportId/sync').post((req, res) => {
  reportDocumentManager
    .synchronizeDocumentReport(req.params.reportId)
    .then((results) => res.FromModel(results))
    .catch((e: Error) => res.Exception(e));
});

module.exports = router;
