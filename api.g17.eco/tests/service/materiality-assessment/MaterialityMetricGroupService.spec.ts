import '../../setup';
import { ObjectId } from 'bson';
import { expect } from 'chai';
import { createSandbox } from 'sinon';
import ContextError from '../../../server/error/ContextError';
import MetricGroup, {
  CustomMetricOrderType,
  MetricGroupModel,
  MetricGroupPlain,
  MetricGroupSource,
  MetricGroupSourceType,
  MetricGroupType,
} from '../../../server/models/metricGroup';
import Survey, { SurveyType } from '../../../server/models/survey';
import { SupportedJobModel } from '../../../server/service/materiality-assessment/background-job/types';
import { getMaterialityAssessmentBackgroundJobService } from '../../../server/service/materiality-assessment/MaterialityAssessmentBackgroundJobService';
import { getMaterialityAssessmentManager } from '../../../server/service/materiality-assessment/MaterialityAssessmentManager';
import { MaterialityAssessmentService } from '../../../server/service/materiality-assessment/MaterialityAssessmentService';
import { MaterialityMetricGroupService } from '../../../server/service/materiality-assessment/MaterialityMetricGroupService';
import { AssessmentType } from '../../../server/types/materiality-assessment';
import { metricGroupOne } from '../../fixtures/metricGroupFixtures';
import { surveyOne } from '../../fixtures/survey';
import { userOne } from '../../fixtures/userFixtures';
import { createMongooseModel } from '../../setup';
import { MaterialityAssessmentScope } from '../../../server/service/materiality-assessment/types';
import { initiativeOneSimple } from '../../fixtures/initiativeFixtures';
import { MaterialPillar } from '../../../server/models/materialTopics';
import { capitalize } from '../../../server/util/string';
import config from '../../../server/config';
import { MetricGroupRepository } from '../../../server/repository/MetricGroupRepository';

describe('MaterialityMetricGroupService', () => {
  const materialityAssessmentManager = getMaterialityAssessmentManager();
  const backgroundJobMTService = getMaterialityAssessmentBackgroundJobService();
  const sandbox = createSandbox();
  const service = new MaterialityMetricGroupService(
    Survey,
    materialityAssessmentManager,
    backgroundJobMTService,
    MetricGroupRepository
  );

  const assessmentOne = {
    ...surveyOne,
    type: SurveyType.Materiality,
    assessmentType: AssessmentType.FinancialMateriality,
    completedDate: new Date(),
  };

  const utrsMapping = Array.from({ length: 20 }, (_, i) => ({
    code: `utr-${i}`,
    score: Math.random() * 100,
  }));

  const utrs = utrsMapping.map(({ code }) => ({ code, _id: new ObjectId() }));

  const limitedTopics = Array.from({ length: 20 }, (_, i) => ({
    code: `topic-${i}`,
    name: `Topic ${i}`,
    categories: { materialPillar: [Object.values(MaterialPillar)[i % 5]] },
    utrMapping: utrsMapping.slice(i, i + 1),
  }));

  const createSubgroup = ({
    pillar,
    subGroupId,
    metricGroup,
  }: {
    pillar: MaterialPillar;
    subGroupId?: ObjectId;
    metricGroup: MetricGroupPlain;
  }) => {
    const utrCodes = limitedTopics
      .filter(({ categories }) => categories.materialPillar.includes(pillar))
      .map(({ utrMapping }) => utrMapping.map(({ code }) => code))
      .flat();
    const utrIds = utrs.filter(({ code }) => utrCodes.includes(code)).map(({ _id }) => _id.toString());
    const _id = subGroupId ?? new ObjectId();

    return {
      _id,
      initiativeId: metricGroup.initiativeId,
      groupName: `Pillar: ${capitalize(pillar)}`,
      code: `${_id.toString()}/${pillar}`,
      groupData: { icon: `${config.assets.cdn}/images/${pillar}.png` },
      universalTrackers: utrIds,
      metricsOrder: { orderType: CustomMetricOrderType.TypeCode },
      type: MetricGroupType.Custom,
      parentId: metricGroup._id,
      createdBy: metricGroup.createdBy,
      source: {
        ...metricGroup.source,
        topicUtrs: utrIds.map((_id) => ({ _id })),
      },
    };
  };

  const createScoreJob = (_id = new ObjectId(), updated = new Date()) =>
    ({ _id, tasks: [{ data: { surveyId: assessmentOne._id } }], updated } as SupportedJobModel);

  const createMetricGroupSource = (overrides: Partial<MetricGroupSource> = {}): MetricGroupSource => ({
    type: MetricGroupSourceType.Survey,
    surveyId: assessmentOne._id,
    jobId: new ObjectId(),
    topTopicsCount: 20,
    topicUtrs: utrs.map(({ _id }) => ({ _id })),
    ...overrides,
  });

  const createMetricGroupDocument = (overrides: Partial<MetricGroupPlain> = {}) =>
    new MetricGroup({
      _id: new ObjectId(),
      type: MetricGroupType.Custom,
      source: createMetricGroupSource(),
      updated: new Date(),
      ...overrides,
    });

  afterEach(() => {
    sandbox.restore();
  });

  describe('generateMetricGroupUtrs', () => {
    it('should throw error if survey is not materiality', async () => {
      sandbox.stub(Survey, 'findOne').returns(createMongooseModel(surveyOne));
      await expect(
        service.generateMetricGroupUtrs({ userId: userOne._id, job: createScoreJob() })
      ).to.eventually.be.rejectedWith(ContextError, 'Survey is not a materiality survey');
    });

    it('should create metric group if not found & generate utrs', async () => {
      sandbox.stub(Survey, 'findOne').returns(createMongooseModel(assessmentOne));
      sandbox.stub(MetricGroup, 'findOne').returns(createMongooseModel(null));
      sandbox
        .stub(materialityAssessmentManager, 'getSizeScopeByAssessmentId')
        .resolves(MaterialityAssessmentScope.MidCap);
      sandbox.stub(MaterialityAssessmentService.prototype, 'getLimitedTopicsWithUtrMappings').resolves({
        limitedTopics: [],
        utrs,
      });
      sandbox.stub(MetricGroup, 'find').returns(createMongooseModel([])); // find subgroups
      sandbox.stub(MetricGroup, 'bulkWrite').resolves(); // for updateOrCreateSubgroups
      
      // Stub save to return the document itself with modifications
      sandbox.stub(MetricGroup.prototype, 'save').callsFake(function(this: MetricGroupModel) {
        return Promise.resolve(this);
      });
      
      const scoreJob = createScoreJob();
      const metricGroupDocument = await service.generateMetricGroupUtrs({ userId: userOne._id, job: scoreJob });

      const expectedSource = {
        surveyId: assessmentOne._id,
        type: 'survey',
        topTopicsCount: 20,
        topicUtrs: utrs.map(({ _id }) => ({ _id })),
        jobId: scoreJob._id,
      };
      const metricGroup = metricGroupDocument.toObject();
      expect(metricGroup.groupName).to.equal('February 2019 (Financial materiality assessment)');
      expect(metricGroup.groupData.colour).to.equal('#9BCDEF');
      expect(metricGroup.source).to.deep.equal(expectedSource);
      expect(metricGroup.universalTrackers).to.deep.equal(utrs.map(({ _id }) => _id));
    });

    it('should skip regenerating utrs if already generated', async () => {
      const metricGroupDocument = createMetricGroupDocument({
        source: createMetricGroupSource(),
      });

      sandbox.stub(Survey, 'findOne').returns(createMongooseModel(assessmentOne));
      sandbox.stub(MetricGroup, 'findOne').returns(createMongooseModel(metricGroupDocument));
      await service.generateMetricGroupUtrs({ userId: userOne._id, job: createScoreJob() });
      const topicLengthSpy = sandbox.spy(materialityAssessmentManager, 'getTopTopicsCount');
      expect(topicLengthSpy.called).to.be.false;
    });

    it('should generate 5 subgroups corresponding to 5 pillars', async () => {
      sandbox.stub(Survey, 'findOne').returns(createMongooseModel(assessmentOne));
      sandbox.stub(MetricGroup, 'findOne').returns(createMongooseModel(null));
      sandbox.stub(MetricGroup, 'find').returns(createMongooseModel([])); // find subgroups
      sandbox
        .stub(materialityAssessmentManager, 'getSizeScopeByAssessmentId')
        .resolves(MaterialityAssessmentScope.MidCap);
      sandbox.stub(MaterialityAssessmentService.prototype, 'getLimitedTopicsWithUtrMappings').resolves({
        limitedTopics,
        utrs,
      });
      const scoreJob = createScoreJob();
      const bulkWriteStub = sandbox.stub(MetricGroup, 'bulkWrite').resolves();
      
      // Stub save to return the document itself with modifications
      sandbox.stub(MetricGroup.prototype, 'save').callsFake(function(this: MetricGroupModel) {
        return Promise.resolve(this);
      });
      
      const metricGroupDocument = await service.generateMetricGroupUtrs({ userId: userOne._id, job: scoreJob });
      const metricGroup = metricGroupDocument.toObject();

      const result = (bulkWriteStub.args[0][0] as { insertOne: { document: any } }[]).map(({ insertOne }) => {
        const { updated, ...group } = insertOne.document;
        return group;
      });

      // Check that 5 pillars were created
      expect(result).to.have.lengthOf(5);
      
      // Check that all pillars are represented
      const pillars = result.map(group => group.code?.split('/')[1]);
      expect(pillars).to.have.members(Object.values(MaterialPillar));
      
      // Check structure of first subgroup as an example
      const firstPillar = result[0];
      expect(firstPillar).to.include.keys('_id', 'code', 'groupName', 'groupData', 'universalTrackers', 'source');
      expect(firstPillar.groupName).to.match(/^Pillar: /);
      expect(firstPillar.initiativeId.toString()).to.equal(metricGroup.initiativeId.toString());
      expect(firstPillar.createdBy.toString()).to.equal(metricGroup.createdBy.toString());
    });
  });

  describe('regenerateMetricGroupUtrs', () => {
    it('should throw error if no source survey', async () => {
      const metricGroupDocument = createMetricGroupDocument();
      sandbox.stub(MetricGroup, 'findById').returns(createMongooseModel(metricGroupDocument));
      await expect(
        service.regenerateMetricGroupUtrs({ groupId: metricGroupOne._id, topTopicsCount: 5 })
      ).to.eventually.be.rejectedWith(ContextError, 'No survey found for metric group');
    });

    it('should throw error if no score job found', async () => {
      const metricGroupDocument = createMetricGroupDocument({ survey: assessmentOne });
      sandbox.stub(MetricGroup, 'findById').returns(createMongooseModel(metricGroupDocument));
      sandbox.stub(backgroundJobMTService, 'findExistingJob').resolves(undefined);
      await expect(
        service.regenerateMetricGroupUtrs({ groupId: metricGroupOne._id, topTopicsCount: 5 })
      ).to.eventually.be.rejectedWith(ContextError, 'No score job found for metric group');
    });

    it('should skip regenerating if nothing changed', async () => {
      const jobId = new ObjectId();
      const topTopicsCount = 5;
      const metricGroupDocument = createMetricGroupDocument({
        source: createMetricGroupSource({ jobId, topTopicsCount }),
        survey: assessmentOne,
        updated: new Date(Date.now() + 1000),
      });
      sandbox.stub(MetricGroup, 'findById').returns(createMongooseModel(metricGroupDocument));
      // same score job
      sandbox.stub(backgroundJobMTService, 'findExistingJob').resolves(createScoreJob(jobId));
      sandbox.stub(MetricGroup, 'countDocuments').resolves(5);
      const topicLengthSpy = sandbox.spy(materialityAssessmentManager, 'getTopTopicsCount');
      await service.regenerateMetricGroupUtrs({
        groupId: metricGroupOne._id,
        // same topic length
        topTopicsCount,
      });
      expect(topicLengthSpy.called).to.be.false;
    });

    it('should regenerating if scores job is regenerated', async () => {
      const newJobId = new ObjectId();
      const metricGroupDocument = createMetricGroupDocument({
        source: createMetricGroupSource(),
        survey: assessmentOne,
      });
      sandbox.stub(MetricGroup, 'findById').returns(createMongooseModel(metricGroupDocument));
      // new score job
      sandbox
        .stub(backgroundJobMTService, 'findExistingJob')
        .resolves(createScoreJob(newJobId, new Date(Date.now() + 1000)));
      sandbox.stub(MaterialityAssessmentService.prototype, 'getLimitedTopicsWithUtrMappings').resolves({
        limitedTopics: [],
        utrs,
      });
      sandbox.stub(MetricGroup, 'countDocuments').resolves(5); // for subgroups check
      sandbox.stub(MetricGroup, 'find').returns(createMongooseModel([])); // find subgroups
      sandbox.stub(MetricGroup, 'bulkWrite').resolves(); // for updateOrCreateSubgroups
      sandbox.stub(metricGroupDocument, 'save').resolves(metricGroupDocument);
      
      const updatedMetricGroupDocument = await service.regenerateMetricGroupUtrs({
        groupId: metricGroupOne._id,
        // same topic length
        topTopicsCount: 20,
      });
      expect(updatedMetricGroupDocument.toObject().source?.jobId).to.deep.equal(newJobId);
    });

    it('should regenerating if scores has updated', async () => {
      const jobId = new ObjectId();
      const metricGroupDocument = createMetricGroupDocument({
        source: createMetricGroupSource(),
        survey: assessmentOne,
      });
      sandbox.stub(MetricGroup, 'findById').returns(createMongooseModel(metricGroupDocument));
      // same score job but has updated
      sandbox
        .stub(backgroundJobMTService, 'findExistingJob')
        .resolves(createScoreJob(jobId, new Date(Date.now() + 1000)));
      sandbox.stub(MaterialityAssessmentService.prototype, 'getLimitedTopicsWithUtrMappings').resolves({
        limitedTopics: [],
        utrs,
      });
      sandbox.stub(MetricGroup, 'countDocuments').resolves(5); // for subgroups check
      sandbox.stub(MetricGroup, 'find').returns(createMongooseModel([])); // find subgroups
      sandbox.stub(MetricGroup, 'bulkWrite').resolves(); // for updateOrCreateSubgroups
      sandbox.stub(metricGroupDocument, 'save').resolves(metricGroupDocument);
      
      const updatedMetricGroupDocument = await service.regenerateMetricGroupUtrs({
        groupId: metricGroupOne._id,
        // same topic length
        topTopicsCount: 20,
      });
      const updatedMetricGroup = updatedMetricGroupDocument.toObject();
      expect(updatedMetricGroup.source?.jobId).to.deep.equal(jobId);
      expect(updatedMetricGroup.source?.topTopicsCount).to.equal(20);
    });

    it('should regenerating if topic length changed', async () => {
      const jobId = new ObjectId();
      const newTopicLength = 10;
      const metricGroupDocument = createMetricGroupDocument({
        source: createMetricGroupSource({ jobId }),
        survey: assessmentOne,
      });
      sandbox.stub(MetricGroup, 'findById').returns(createMongooseModel(metricGroupDocument));
      // same score job
      sandbox.stub(backgroundJobMTService, 'findExistingJob').resolves(createScoreJob(jobId));
      sandbox.stub(MaterialityAssessmentService.prototype, 'getLimitedTopicsWithUtrMappings').resolves({
        limitedTopics: [],
        utrs,
      });
      sandbox.stub(MetricGroup, 'countDocuments').resolves(5); // for subgroups check
      sandbox.stub(MetricGroup, 'find').returns(createMongooseModel([])); // find subgroups
      sandbox.stub(MetricGroup, 'bulkWrite').resolves(); // for updateOrCreateSubgroups
      sandbox.stub(metricGroupDocument, 'save').resolves(metricGroupDocument);
      
      const updatedMetricGroupDocument = await service.regenerateMetricGroupUtrs({
        groupId: metricGroupOne._id,
        // new topic length
        topTopicsCount: newTopicLength,
      });
      const updatedMetricGroup = updatedMetricGroupDocument.toObject();
      expect(updatedMetricGroup.source?.jobId).to.deep.equal(jobId);
      expect(updatedMetricGroup.source?.topTopicsCount).to.deep.equal(newTopicLength);
    });

    it('should generate subgroups if no subgroups found', async () => {
      const jobId = new ObjectId();
      const topTopicsCount = 5;
      const metricGroupDocument = createMetricGroupDocument({
        source: createMetricGroupSource({ jobId, topTopicsCount }),
        survey: assessmentOne,
        updated: new Date(Date.now() + 1000), // is up to date
      });
      sandbox.stub(MetricGroup, 'findById').returns(createMongooseModel(metricGroupDocument));
      // same score job
      sandbox.stub(backgroundJobMTService, 'findExistingJob').resolves(createScoreJob(jobId));
      sandbox.stub(MetricGroup, 'countDocuments').resolves(0); // no subgroups generated
      sandbox.stub(MaterialityAssessmentService.prototype, 'getLimitedTopicsWithUtrMappings').resolves({
        limitedTopics: [],
        utrs,
      });
      sandbox.stub(MetricGroup, 'find').returns(createMongooseModel([])); // find subgroups
      const topicLengthSpy = sandbox.spy(materialityAssessmentManager, 'getTopTopicsCount');
      const bulkWriteStub = sandbox.stub(MetricGroup, 'bulkWrite').resolves();
      sandbox.stub(metricGroupDocument, 'save').resolves(metricGroupDocument);
      
      await service.regenerateMetricGroupUtrs({
        groupId: metricGroupOne._id,
        // same topic length
        topTopicsCount,
      });

      expect(topicLengthSpy.calledOnce).to.be.true;
      const result = bulkWriteStub.firstCall.args[0] as { insertOne: { document: any } }[];
      const subgroupNames = result.map(({ insertOne }) => insertOne.document.groupName);
      const expectedSubgroupNames = Object.values(MaterialPillar).map((pillar) => `Pillar: ${capitalize(pillar)}`);
      expect(subgroupNames).to.deep.equal(expectedSubgroupNames);
    });

    it('should regenerate subgroups if metric groups is regenerated', async () => {
      const newJobId = new ObjectId();
      const metricGroupDocument = createMetricGroupDocument({
        source: createMetricGroupSource(),
        survey: assessmentOne,
      });
      const metricGroup = metricGroupDocument.toObject();
      sandbox.stub(MetricGroup, 'findById').returns(createMongooseModel(metricGroupDocument));
      // new score job
      sandbox
        .stub(backgroundJobMTService, 'findExistingJob')
        .resolves(createScoreJob(newJobId, new Date(Date.now() + 1000)));
      sandbox
        .stub(MaterialityAssessmentService.prototype, 'getLimitedTopicsWithUtrMappings')
        .resolves({ limitedTopics, utrs });
      sandbox.stub(MetricGroup, 'countDocuments').resolves(5); // for subgroups check
      sandbox
        .stub(MetricGroup, 'find')
        .returns(
          createMongooseModel(Object.values(MaterialPillar).map((pillar) => createSubgroup({ pillar, metricGroup })))
        );
      const bulkWriteStub = sandbox.stub(MetricGroup, 'bulkWrite').resolves();
      sandbox.stub(metricGroupDocument, 'save').resolves(metricGroupDocument);
      
      const updatedMetricGroupDocument = await service.regenerateMetricGroupUtrs({
        groupId: metricGroupOne._id,
        // same topic length
        topTopicsCount: 20,
      });
      expect(updatedMetricGroupDocument.toObject().source?.jobId).to.deep.equal(newJobId);
      const result = bulkWriteStub.firstCall.args[0] as { updateOne: any; insertOne: any }[];
      expect(result.map(({ updateOne }) => updateOne).length).to.equal(5);
      expect(result.map(({ insertOne }) => insertOne).filter(Boolean).length).to.equal(0);
    });
  });

  describe('getLatestMetricGroup', () => {
    let findSurveyStub: sinon.SinonStub;
    let findMetricGroupStub: sinon.SinonStub;
    let findScoreJobStub: sinon.SinonStub;
    let generateUtrsStub: sinon.SinonStub;
    let getSubgroupsStub: sinon.SinonStub;

    beforeEach(() => {
      findSurveyStub = sandbox.stub(Survey, 'findOne');
      findMetricGroupStub = sandbox.stub(MetricGroup, 'findOne');
      findScoreJobStub = sandbox.stub(backgroundJobMTService, 'findExistingJob').resolves(createScoreJob());
      generateUtrsStub = sandbox.stub(service, 'processGenerateUtrs');
      getSubgroupsStub = sandbox.stub(MetricGroupRepository, 'getAllChildrenById');
    });

    it('should return undefined if no completed assessment is found', async () => {
      findSurveyStub.returns(createMongooseModel(null));
      const result = await service.getLatestMetricGroup(initiativeOneSimple, userOne._id);
      expect(result).to.be.undefined;
    });

    it('should return metric group associated with latest completed assessment', async () => {
      findSurveyStub.returns(createMongooseModel(assessmentOne));
      const metricGroupDocument = createMetricGroupDocument({
        survey: assessmentOne,
        source: createMetricGroupSource(),
      });
      findMetricGroupStub.returns(createMongooseModel(metricGroupDocument));
      getSubgroupsStub.returns([]);
      const result = await service.getLatestMetricGroup(initiativeOneSimple, userOne._id);

      expect(result).to.deep.equal({
        metricGroupId: metricGroupDocument._id,
        subGroupIds: [],
        effectiveDate: assessmentOne.effectiveDate,
      });
      expect(findScoreJobStub.called).not.to.be.true;
      expect(generateUtrsStub.called).not.to.be.true;
    });

    it("should generate UTRS if empty, return latest assessment's metric group", async () => {
      findSurveyStub.returns(createMongooseModel(assessmentOne));
      const metricGroupDocument = createMetricGroupDocument({ source: undefined });
      findMetricGroupStub.returns(createMongooseModel(metricGroupDocument));
      const subGroupIds = Array.from({ length: 5 }).map(() => new ObjectId());
      getSubgroupsStub.returns(subGroupIds.map((_id) => ({ _id })));
      const result = await service.getLatestMetricGroup(initiativeOneSimple, userOne._id);

      expect(result).to.deep.equal({
        metricGroupId: metricGroupDocument._id,
        subGroupIds,
        effectiveDate: assessmentOne.effectiveDate,
      });
      expect(findScoreJobStub.calledOnce).to.be.true;
      expect(generateUtrsStub.calledOnce).to.be.true;
    });
  });
});
