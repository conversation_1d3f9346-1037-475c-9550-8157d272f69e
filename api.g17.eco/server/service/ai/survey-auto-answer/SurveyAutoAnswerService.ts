/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import type { C<PERSON>Job} from '../../../models/backgroundJob';
import BackgroundJob, { JobType, TaskStatus, TaskType } from '../../../models/backgroundJob';
import ContextError from '../../../error/ContextError';
import type { ProcessedAnswerResult, CreatedJob, TaskAIAutoAnswerSetup, WorkflowCreate, AutoAnswerUtrv } from './types';
import { ObjectId } from 'bson';
import { getCurrentDateStr } from '../../../util/date';
import { createLogEntry } from '../../../service/jobs';
import type {
  BackgroundJobService} from '../../../service/background-process/BackgroundJobService';
import {
  getBackgroundJobService,
} from '../../../service/background-process/BackgroundJobService';
import type { LoggerInterface} from '../../../service/wwgLogger';
import { wwgLogger } from '../../../service/wwgLogger';
import { generatedUUID } from '../../../service/crypto/token';
import type { SurveyWithInitiative } from '../../../models/survey';
import Survey, { SurveyType } from '../../../models/survey';
import type { UniversalTrackerValueModelExtended } from '../../../models/universalTrackerValue';
import UniversalTrackerValue from '../../../models/universalTrackerValue';
import { getReportName } from '../../../util/survey';
import type { InitiativePlain } from '../../../models/initiative';
import { UtrValueType } from '../../../models/public/universalTrackerType';
import UniversalTrackerActionManager from '../../../service/utr/UniversalTrackerActionManager';
import type { UniversalTrackerValueListPlain } from '../../../models/universalTracker';
import UniversalTracker from '../../../models/universalTracker';
import { universalTrackerPlainFields } from '../../../repository/projections';
import type { PredictedDataResolver } from '../PredictedDataResolver';
import { getPredictedDataResolver } from '../PredictedDataResolver';
import { ValueDataSourceType } from '../../../models/public/universalTrackerValueType';
import type { AiService} from '../service';
import { getAiService } from '../service';
import type { AIUtrvSuggestion } from '../types';
import type { AppSettingsService} from '../../app-settings/AppSettingsService';
import { getAppSettingsService } from '../../app-settings/AppSettingsService';
import { AppSettingKey } from '../../../models/app-setting';
import { AIModelType, getAIModelFactory } from '../AIModelFactory';
import type { ChatGPT } from '../models/ChatGPT';
import type { FileSupportAiModel } from '../models/FileSupportAiModel';
import { ToolType } from '../constants';
import { ActionList } from '../../../types/constants';

export class SurveyAutoAnswerService {
  constructor(
    private logger: LoggerInterface,
    private bgJobService: BackgroundJobService,
    private dataResolver: PredictedDataResolver,
    private aiService: AiService,
    private appSettingsService: AppSettingsService,
    private aiModel: FileSupportAiModel,
  ) {}

  private getUtrvStatusMatch(isOverwriteMetric: boolean) {
    if (!isOverwriteMetric) {
      return { status: ActionList.Created };
    }
    return {
      status: { $in: [ActionList.Created, ActionList.Updated, ActionList.Verified, ActionList.Rejected] },
      assuranceStatus: { $exists: false },
    };
  }

  private async getSetupTask({
    survey,
    useDocumentLibrary,
    isOverwriteMetric,
  }: { survey: SurveyWithInitiative } & Pick<
    WorkflowCreate,
    'useDocumentLibrary' | 'isOverwriteMetric'
  >): Promise<TaskAIAutoAnswerSetup> {
    const utrvStatusMatch = this.getUtrvStatusMatch(isOverwriteMetric);
    const utrvs = await UniversalTrackerValue.aggregate<AutoAnswerUtrv>([
      {
        $match: {
          _id: { $in: survey.visibleUtrvs },
          deletedDate: { $exists: false },
          // Skip private metrics from AI processing
          isPrivate: { $ne: true },
          ...utrvStatusMatch,
        },
      },
      {
        $lookup: {
          from: 'universal-trackers',
          localField: 'universalTrackerId',
          foreignField: '_id',
          as: 'universalTracker',
        },
      },
      {
        $unwind: '$universalTracker',
      },
      {
        $project: {
          _id: 1,
          universalTrackerId: 1,
          utrCode: '$universalTracker.code',
        },
      },
    ]).exec();

    return {
      id: generatedUUID(),
      name: 'Set up utrvs for sequential automatic answering',
      type: TaskType.AIAutoAnswerSetup,
      status: TaskStatus.Pending,
      data: {
        utrvs,
        surveyId: survey._id,
        useDocumentLibrary,
        isOverwriteMetric
      },
    };
  }

  /**
   * Workflow can contain these tasks
   *  1. Setup created utrvs of the survey, each utrv is processed within a task
   *  2. If using document library, fetch related documents, upload to AI otherwise skip
   *  3. Process through each utrv, use AI and document (if attached) to answer, and save suggested data
   *  4. Clean up temporary files if needed
   *  5. Complete with a notification
   */
  public async createJob(workflow: WorkflowCreate & { idempotencyKey: string }): Promise<CreatedJob> {
    const { initiativeId, surveyId, userId, idempotencyKey, useDocumentLibrary, isOverwriteMetric } = workflow;

    const survey = await Survey.findById(surveyId).populate('initiative').orFail().lean<SurveyWithInitiative>().exec();
    if (survey.type !== SurveyType.Default) {
      throw new ContextError('Only default survey type is supported for automatic answer', {
        surveyId,
        surveyType: survey.type,
      });
    }

    const setupTask = await this.getSetupTask({ survey, useDocumentLibrary, isOverwriteMetric });

    const createData: CreateJob = {
      _id: new ObjectId(),
      idempotencyKey,
      type: JobType.AIAutoAnswerSurvey,
      name: `${getCurrentDateStr()} AI automatic answer survey: ${getReportName(survey)}`,
      tasks: [setupTask],
      logs: [createLogEntry('Starting the automatic answer workflow')],
      userId,
      initiativeId,
    };
    const job = await BackgroundJob.create(createData);

    this.bgJobService.runFromJob(job).catch((e) => {
      this.logger.error(
        new ContextError(`Failed to complete background job type ${job.type}`, {
          debugMessage: `Job "${job.name}" failed to complete`,
          jobId: job._id,
          jobType: job.type,
          initiativeId: job.initiativeId,
          cause: e,
        })
      );
    });

    return {
      jobId: job._id.toString(),
      status: job.status,
    };
  }

  private async applyPredictedAnswer({
    utrv,
    initiative,
    predictedAnswer,
    userId,
    jobId,
    modelVersion,
  }: {
    utrv: UniversalTrackerValueModelExtended;
    initiative: InitiativePlain;
    predictedAnswer: AIUtrvSuggestion['predictedAnswer'] | null;
    userId: ObjectId;
    jobId: ObjectId;
    modelVersion: string;
  }): Promise<ProcessedAnswerResult> {
    if (!predictedAnswer) {
      return {
        isSuccess: false,
        errorMessage: 'Not found any predicted answer from AI',
      };
    }

    const utr = await UniversalTracker.findById(utrv.universalTracker._id, universalTrackerPlainFields)
      .populate('valueListOptions')
      .populate('tableColumnValueListOptions')
      .lean<UniversalTrackerValueListPlain>()
      .exec();

    if (!utr) {
      return {
        isSuccess: false,
        errorMessage: `Not found a UTR of this utrv: ${utrv._id.toString()}`,
      };
    }

    if ([UtrValueType.Date].includes(utr.valueType)) {
      return {
        isSuccess: false,
        errorMessage: 'Not yet support this metric type',
      };
    }

    const { value, valueData } = this.dataResolver.getValueDataPredictedAnswer({ utr, predictedAnswer });

    if (!value && !valueData) {
      return {
        isSuccess: false,
        errorMessage: 'Not have actual data to update',
      };
    }

    // hydrate valueData for additional AI information
    if (valueData) {
      valueData.source = {
        type: ValueDataSourceType.AIAutoAnswer,
        data: {
          model: modelVersion,
          backgroundJobId: jobId,
        },
      };
    }

    // predicted answer use default utr props (no input, so we can use the same)
    const noteResponse = await this.aiService.getFurtherNotesDraft({
      utrv,
      initiative,
      draftData: {
        value,
        unit: utr.unit,
        numberScale: utr.numberScale,
        // Table will figure out  from utrv.universalTracker
        valueData,
      },
      userId,
    });
    const note = noteResponse.content;

    UniversalTrackerActionManager.hydrateUpdate({
      utrv,
      userId,
      value,
      valueData,
      note,
    });

    await utrv.save();

    return {
      isSuccess: true,
      value,
      valueData,
      note,
    };
  }

  public async processAnswerUtrv({
    initiative,
    utrvId,
    userId,
    jobId,
    isOverwriteMetric = false,
    relatedDocumentIds = [],
    assistantId,
  }: {
    initiative: InitiativePlain;
    utrvId: ObjectId;
    userId: ObjectId;
    jobId: ObjectId;
    isOverwriteMetric?: boolean;
    relatedDocumentIds?: string[];
    assistantId?: string;
  }): Promise<ProcessedAnswerResult> {
    const utrvStatusMatch = this.getUtrvStatusMatch(isOverwriteMetric);
    const utrv = (await UniversalTrackerValue.findOne({
      _id: utrvId,
      deletedDate: { $exists: false },
      // Skip private metrics from AI processing
      isPrivate: { $ne: true },
      ...utrvStatusMatch,
    })
      .populate('universalTracker')
      .exec()) as UniversalTrackerValueModelExtended;

    if (!utrv) {
      return {
        isSuccess: false,
        errorMessage: `Utrv not matched: ${utrvId}`,
      };
    }

    const {
      content: { predictedAnswer },
      modelVersion,
    } =
      relatedDocumentIds.length === 0 || !assistantId
        ? await this.aiService.getUtrvAssistantResponse({ initiative, utrv })
        : await this.aiService.getDocumentUtrvAssistantResponse({ initiative, utrv, relatedDocumentIds, assistantId });

    return this.applyPredictedAnswer({
      utrv,
      initiative,
      predictedAnswer,
      userId,
      jobId,
      modelVersion,
    });
  }

  public async getOrCreateAutoAnswerAssistantId() {
    const existedAssistantId = await this.appSettingsService.getAppSettingsByKey(
      AppSettingKey.OpenAIAutoAnswerAssistantId
    );

    if (existedAssistantId) {
      return existedAssistantId.value;
    }

    this.logger.info('Assistant not found. Creating Auto Answer Assistant...');

    const assistantInstructions = `
      You are an AI assistant that uses uploaded documents to answer sustainability metrics called UTRs (Universal Trackers) based on the documents' content.

      You will be given these attachments:
      1. A UTR object represents the question to answer.
      2. A few document files (PDF, Word, image, or plain text) that contains narrative, data, or evidence to analyze.

      Your task is to:
      - Analyze the content of those documents.
      - Supply an answer for that UTR using the content.
      `.trim();

    const assistant = await this.aiModel.createAssistant({
      name: 'UTR Document Answerer',
      instructions: assistantInstructions,
      tools: [{ type: ToolType.CodeInterpreter }],
    });
    const assistantId = assistant.id;

    this.logger.info('Assistant created', { assistantId, assistantModel: assistant.model });

    await this.appSettingsService.create({
      key: AppSettingKey.OpenAIAutoAnswerAssistantId,
      value: assistantId,
    });

    return assistantId;
  }
}

let instance: SurveyAutoAnswerService;
export const getSurveyAutoAnswerService = () => {
  if (!instance) {
    instance = new SurveyAutoAnswerService(
      wwgLogger,
      getBackgroundJobService(),
      getPredictedDataResolver(),
      getAiService(),
      getAppSettingsService(),
      getAIModelFactory().getAiModel(AIModelType.ChatGPT) as ChatGPT
    );
  }
  return instance;
};
