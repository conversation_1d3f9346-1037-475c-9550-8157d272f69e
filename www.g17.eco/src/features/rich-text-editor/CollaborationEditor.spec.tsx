/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { screen } from '@testing-library/react';
import { CollaborationEditor, CollaborationEditorProps } from './CollaborationEditor';
import { ws } from 'msw';
import config from '../../config';
import { useProviderFactory } from '@features/rich-text-editor/useProviderFactory';
import { afterEach } from 'vitest';
import { setupServer } from 'msw/node';
import { renderWithProviders } from '@fixtures/utils';
import { simpleUserStore } from '@fixtures/redux-store';
import { userEvent } from '@testing-library/user-event';


describe('CollaborationEditor', () => {

  const webSocketLink = ws.link(config.websocketUrl + '/*');

  const handlers = [
    webSocketLink.addEventListener('connection', ({ client }) => {
      console.log('Intercepted a WebSocket connection:', client.id);

      // Handle incoming messages from the client
      client.addEventListener('message', (event) => {
        event.preventDefault();
      });
    }),
  ];

  const server = setupServer(...handlers)

  beforeAll(() => server.listen())
  afterEach(() => {
    server.resetHandlers();
  })
  afterAll(() => server.close())

  const renderOptions = {
    store: simpleUserStore,
    route: { path: '/', initialEntries: ['/'] }
  };

  const WrapperWithProvider = (props: Omit<CollaborationEditorProps, 'providerFactory'>) => {
    const { providerFactory } = useProviderFactory();
    return (
      <>
        <CollaborationEditor{...props} providerFactory={providerFactory} />;
      </>
    )
  }

  it('should render the editor with the correct placeholder', () => {
    renderWithProviders(
      <WrapperWithProvider
        placeholder={() => <div>Type here...</div>}
        documentId='test-doc' />,
      renderOptions
    );

    expect(screen.getByText('Type here...')).toBeInTheDocument();
  });

  it('should allow typing in the editor', async () => {
    const user = userEvent.setup();

    renderWithProviders(
      <WrapperWithProvider
        placeholder={() => <div>Type here...</div>}
        documentId='test-doc-typing'
      />,
      renderOptions
    );

    const contentEditable = screen.getByRole('textbox');
    expect(contentEditable).toBeInTheDocument();

    const text = 'Hello, world!';
    await user.click(contentEditable);
    await user.type(contentEditable, text);

    // Seems like we need to use proper DOM events to trigger the input event
    // as that is not supported in JS-DOM properly, therefore this should be done
    // in real browser environment, likely playwright
    //
    // @link: https://github.com/facebook/lexical/issues/4595
    // https://playwright.dev/docs/test-components
    // @TODO: Implement this test in playwright
    // expect(screen.getByRole('textbox').textContent).toBe(text);
    expect(screen.getByRole('textbox').textContent).toBe('');
  });

});
