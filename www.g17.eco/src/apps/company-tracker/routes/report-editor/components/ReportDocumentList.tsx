/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { ReportDocument } from '@g17eco/types/reportDocument';
import { DashboardSection } from '@g17eco/molecules/dashboard';
import { Button } from 'reactstrap';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import IconButton from '@g17eco/molecules/button/IconButton';
import { formatDateUTC } from '@utils/date';
import { useDeleteReportDocumentMutation } from '@api/initiative-report-documents';
import { Loader } from '@g17eco/atoms/loader';

interface Props {
  list: ReportDocument[];
  type?: string;
  onView: (report: ReportDocument) => void;
  onEdit: (report: ReportDocument) => void;
  viewCreate: () => void;
}

export const ReportDocumentList = (props: Props) => {
  const { list, type, onView, onEdit, viewCreate } = props;
  const [deleteReport, { isLoading: isDeleting }] = useDeleteReportDocumentMutation();

  const reports = type ? list.filter((report) => report.type === type) : list;

  const buttons = [
    <Button disabled={isDeleting} color={'primary'} key={'create'} onClick={viewCreate}>
      Create
    </Button>,
  ];

  const onDelete = (report: ReportDocument) => {
    if (!window.confirm('Are you sure you want to delete this report?')) {
      return;
    }
    deleteReport({ initiativeId: report.initiativeId, reportId: report._id });
  };

  const columns: ColumnDef<ReportDocument>[] = [
    {
      header: 'Report',
      accessorKey: 'title',
      meta: {
        cellProps: {
          style: {
            width: 350,
            maxWidth: 350,
          },
        },
      },
    },
    {
      accessorKey: 'description',
    },
    {
      accessorKey: 'type',
    },
    {
      id: 'created',
      accessorFn: (value) => formatDateUTC(value.created),
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        return (
          <div>
            <IconButton
              disabled={isDeleting}
              outline={false}
              color='transparent'
              icon='fal fa-eye'
              onClick={() => onView(row.original)}
            />
            <IconButton
              disabled={isDeleting}
              outline={false}
              color='transparent'
              icon='fal fa-pencil'
              onClick={() => onEdit(row.original)}
            />
            <IconButton
              disabled={isDeleting}
              outline={false}
              color='transparent'
              icon='fal fa-trash'
              onClick={() => onDelete(row.original)}
            />
          </div>
        );
      },
    },
  ];

  return (
    <DashboardSection title={`Report Document List (${(type ?? 'all').toUpperCase()})`} buttons={buttons}>
      {isDeleting ? <Loader /> : null}
      <Table columns={columns} data={reports} />
    </DashboardSection>
  );
};
