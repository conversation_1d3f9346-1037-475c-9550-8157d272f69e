import { g17ecoApi, transformResponse } from '@api/g17ecoApi';
import { CreateReportDocumentMin, ReportDocument, type ReportDocumentStatus } from '@g17eco/types/reportDocument';
import { SerializedEditorState } from 'lexical';

interface PartnerGet {
  initiativeId: string;
  reportId: string;
}

enum Tags {
  ReportDocuments = 'report-documents',
  ReportDocument = 'report-document',
  ReportDocumentTemplate = 'report-document-template',
}

export const reportDocumentApi = g17ecoApi
  .enhanceEndpoints({
    addTagTypes: [Tags.ReportDocuments, Tags.ReportDocument, Tags.ReportDocumentTemplate],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      listReportDocuments: builder.query<ReportDocument[], { initiativeId: string }>({
        transformResponse,
        query: ({ initiativeId }) => ({
          url: `/initiatives/${initiativeId}/report-documents`,
          method: 'get',
        }),
        providesTags: [Tags.ReportDocuments],
      }),
      getReportDocument: builder.query<ReportDocument, PartnerGet>({
        transformResponse,
        query: ({ initiativeId, reportId }) => ({
          url: `/initiatives/${initiativeId}/report-documents/${reportId}`,
          method: 'get',
        }),
        providesTags: (_result, _error, arg) => [{ type: Tags.ReportDocument, id: arg.reportId }],
      }),
      createReportDocument: builder.mutation<ReportDocument, CreateReportDocumentMin>({
        transformResponse,
        query: (data) => ({
          url: `/initiatives/${data.initiativeId}/report-documents`,
          method: 'post',
          data: data,
        }),
        invalidatesTags: [Tags.ReportDocuments],
      }),
      updateReportDocument: builder.mutation<ReportDocument, ReportDocument>({
        transformResponse,
        query: (data) => ({
          url: `/initiatives/${data.initiativeId}/report-documents/${data._id}`,
          method: 'put',
          data: data,
        }),
        invalidatesTags: (_result, _error, agr) => {
          return [Tags.ReportDocuments, { type: Tags.ReportDocument, id: agr._id }];
        },
      }),
      getReportDocumentTemplate: builder.query<
        SerializedEditorState | undefined,
        { initiativeId: string; reportId: string }
      >({
        transformResponse,
        query: ({ initiativeId, reportId }) => ({
          url: `initiatives/${initiativeId}/report-documents/${reportId}/template`,
          method: 'get',
        }),
        providesTags: (_result, _error, { reportId }) => [{ type: Tags.ReportDocumentTemplate, id: reportId }],
      }),
      downloadReportDocument: builder.mutation<
        { filename: string; xmlString: string },
        { initiativeId: string; reportId: string; editorState: SerializedEditorState }
      >({
        transformResponse,
        query: ({ initiativeId, reportId, editorState }) => ({
          url: `initiatives/${initiativeId}/report-documents/${reportId}/download`,
          method: 'post',
          data: {
            editorState,
          },
        }),
        invalidatesTags: (_result, _error, { reportId }) => [{ type: Tags.ReportDocumentTemplate, id: reportId }],
      }),
      deleteReportDocument: builder.mutation<
        { _id: string; message: string },
        { initiativeId: string; reportId: string }
      >({
        transformResponse,
        query: ({ initiativeId, reportId }) => ({
          url: `initiatives/${initiativeId}/report-documents/${reportId}`,
          method: 'delete',
        }),
        invalidatesTags: [Tags.ReportDocuments],
      }),
      initializeReportDocument: builder.query<
        { status: ReportDocumentStatus; lexicalState?: SerializedEditorState },
        { initiativeId: string; reportId: string }
      >({
        transformResponse,
        query: ({ initiativeId, reportId }) => ({
          url: `initiatives/${initiativeId}/report-documents/${reportId}/initialize-state`,
          method: 'get',
        }),
        providesTags: (_result, _error, { reportId }) => [{ type: Tags.ReportDocument, id: reportId }],
      }),
    }),
  });

export const {
  useListReportDocumentsQuery,
  useGetReportDocumentQuery,
  useCreateReportDocumentMutation,
  useUpdateReportDocumentMutation,
  useLazyGetReportDocumentTemplateQuery,
  useDownloadReportDocumentMutation,
  useDeleteReportDocumentMutation,
  useInitializeReportDocumentQuery,
} = reportDocumentApi;
