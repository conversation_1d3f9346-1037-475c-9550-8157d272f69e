import { HydratedDocument } from 'mongoose';
import { BackgroundBaseWorkflow, JobInfo, TaskResult } from '../background-process/BackgroundBaseWorkflow';
import BackgroundJob, {
  activeJobStatuses,
  BackgroundJobPlain,
  CreateJob,
  JobType,
  Task,
  TaskStatus,
  TaskType,
} from '../../models/backgroundJob';
import ContextError from '../../error/ContextError';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { BackgroundJobService, getBackgroundJobService } from '../background-process/BackgroundJobService';
import { generatedUUID } from '../crypto/token';
import { ObjectId } from 'bson';
import UserError from '../../error/UserError';
import { createLogEntry } from '../jobs';
import { DateFormat, getCurrentDateStr, getPastDate } from '../../util/date';
import moment from 'moment';
import { SuccessReportManager, getSuccessReportManager } from './SuccessReportManager';

type GenerateSurveyEngagementReportTask = Task<{ filePath?: string }, TaskType.GenerateSurveyEngagementReport>;
type GenerateCompanyReportTask = Task<{ filePath?: string }, TaskType.GenerateCompanyUpgradesReport>;
export type GenerateSuccessReportTask = GenerateSurveyEngagementReportTask | GenerateCompanyReportTask;

type SurveyEngagementJobPlain = BackgroundJobPlain<GenerateSuccessReportTask[]> & {
  type: JobType.SuccessReport;
};

export type SurveyEngagementJob = HydratedDocument<SurveyEngagementJobPlain>;

export class SuccessReportWorkflow extends BackgroundBaseWorkflow<SurveyEngagementJob> {
  constructor(
    protected logger: LoggerInterface,
    protected jobType: JobType.SuccessReport,
    private bgJobService: BackgroundJobService,
    private successReportManager: SuccessReportManager
  ) {
    super();
  }

  private async createJob(task: GenerateSurveyEngagementReportTask) {
    const createData: CreateJob = {
      _id: new ObjectId(),
      type: this.jobType,
      name: `${getCurrentDateStr()} ${TaskType.GenerateSurveyEngagementReport}`,
      tasks: [task],
      logs: [createLogEntry(`Starting ${TaskType.GenerateSurveyEngagementReport} workflow`)],
    };

    return (await BackgroundJob.create(createData)) as SurveyEngagementJob;
  }

  public async create(): Promise<JobInfo> {
    // Ensure we are not spamming the same job again and again.
    // Might need to be smarter on how we detect it later.
    const exists = await BackgroundJob.exists({
      type: this.jobType,
      status: { $in: activeJobStatuses },
      // Only check for jobs created in the last 24 hours, so one failed job will not block forever
      created: { $gte: getPastDate(24, 'hour') },
    });

    if (exists) {
      throw new UserError(`Survey engagement export job already exists for this configuration`, {
        jobType: this.jobType,
        existingJobId: exists._id,
      });
    }

    const generateTask: GenerateSurveyEngagementReportTask = {
      id: generatedUUID(),
      name: 'Generate survey engagement report',
      type: TaskType.GenerateSurveyEngagementReport,
      status: TaskStatus.Pending,
      data: {},
    };

    const job = await this.createJob(generateTask);

    this.bgJobService.runFromJob(job).catch((e) => {
      this.logger.error(
        new ContextError(`Failed to complete background job type ${job.type}`, {
          debugMessage: `Job "${job.name}" failed to complete`,
          jobId: job._id,
          jobType: job.type,
          initiativeId: job.initiativeId,
          cause: e,
        })
      );
    });

    return {
      jobId: job._id,
      taskId: generateTask.id,
    };
  }

  private async generateSurveyEngagementData(job: SurveyEngagementJob, task: GenerateSurveyEngagementReportTask) {
    await this.startTask(job, task);

    const filePath = await this.successReportManager.getFilePath({
      reportType: TaskType.GenerateSurveyEngagementReport,
      folder: `jobs/${job._id}/${task.id}`,
      filename: `Survey engagement-${moment().format(DateFormat.FileName)}`,
    });

    this.logger.info(`Job is processed with all successful task: ${TaskType.GenerateSurveyEngagementReport}`, {
      jobId: job._id,
      jobType: job.type,
      taskId: task.id,
      taskType: task.type,
      path: filePath,
    });

    task.data.filePath = filePath;
    task.status = TaskStatus.Completed;
    task.completedDate = new Date();

    // create next task
    const nextTask: GenerateCompanyReportTask = {
      id: generatedUUID(),
      name: 'Generate company onboarding and upgrades report',
      type: TaskType.GenerateCompanyUpgradesReport,
      status: TaskStatus.Pending,
      data: {},
    };

    job.tasks.push(nextTask);
    job.logs.push(
      createLogEntry(`Completed ${task.name} processing`, {
        metadata: {
          taskId: task.id,
        },
      })
    );
    job.markModified('tasks');
    return job.save();
  }

  private async generateCompanyUpgradesData(job: SurveyEngagementJob, task: GenerateCompanyReportTask) {
    await this.startTask(job, task);

    const filePath = await this.successReportManager.getFilePath({
      reportType: TaskType.GenerateCompanyUpgradesReport,
      folder: `jobs/${job._id}/${task.id}`,
      filename: `Company onboarding and upgrades-${moment().format(DateFormat.FileName)}`,
    });

    this.logger.info(`Job is completed with all successful tasks`, {
      jobId: job._id,
      jobType: job.type,
      taskId: task.id,
      taskType: task.type,
      path: filePath,
    });

    task.data.filePath = filePath;
    task.status = TaskStatus.Completed;
    task.completedDate = new Date();

    job.logs.push(
      createLogEntry(`Completed ${task.name} processing`, {
        metadata: {
          taskId: task.id,
        },
      })
    );
    job.markModified('tasks');
    return job.save();
  }

  public async processTask(
    job: SurveyEngagementJob,
    task: GenerateSuccessReportTask
  ): Promise<TaskResult<SurveyEngagementJob>> {
    switch (task.type) {
      case TaskType.GenerateSurveyEngagementReport:
        return {
          job: await this.generateSurveyEngagementData(job, task),
          executeNextTask: true,
        };
      case TaskType.GenerateCompanyUpgradesReport:
        return {
          job: await this.generateCompanyUpgradesData(job, task),
          executeNextTask: true, // run next task to complete the job
        };
      default:
        throw new ContextError(`Found not handled job ${job._id} task type ${job.type}`, {
          jobId: job._id,
        });
    }
  }
}

let instance: SuccessReportWorkflow;
export const getSuccessReportWorkflow = () => {
  if (!instance) {
    instance = new SuccessReportWorkflow(
      wwgLogger,
      JobType.SuccessReport,
      getBackgroundJobService(),
      getSuccessReportManager()
    );
  }
  return instance;
};
