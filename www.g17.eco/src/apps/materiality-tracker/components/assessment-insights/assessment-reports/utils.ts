import { MaterialityBoundary, MaterialPillar } from '@apps/materiality-tracker/api/materiality-assessment';
import {
  AlignmentType,
  ILevelsOptions,
  ImageRun,
  IParagraphStyleOptions,
  LevelFormat,
  Percentage,
  TableLayoutType,
  WidthType,
} from 'docx';
import { DataSource } from '@features/report-output/common';
import { paragraph } from '@features/report-output/document-structure';
import { BulletFormatLevel } from '@features/report-output/styles';

export const MAX_SUGGESTED_TOPIC = 2;

export const STYLES = {
  TABLE_BORDER_COLOUR: '6a6c70',
  TABLE_CELL: {
    size: 20,
    color: '434343',
    alignment: AlignmentType.LEFT,
  },
  TABLE: {
    width: {
      size: '100%' as Percentage,
      type: WidthType.PERCENTAGE,
    },
    layout: TableLayoutType.AUTOFIT,
  },
  APPENDIX_TABLE_HEADER: {
    textStyles: { color: 'ffffff' },
    cellStyles: {
      shading: { fill: '351c75' },
    },
  },
};

export const MARGINS_SM = {
  left: 75,
  right: 75,
  top: 75,
  bottom: 75,
};

export const boundariesMap = {
  [MaterialityBoundary.Leadership]: 'Leadership',
  [MaterialityBoundary.ResearchAndDevelopment]: 'R&D',
  [MaterialityBoundary.SupplyChain]: 'SCM',
  [MaterialityBoundary.ProductAndServices]: 'Service',
  [MaterialityBoundary.Distribution]: 'Distribution',
  [MaterialityBoundary.Communities]: 'Community',
  [MaterialityBoundary.Experiences]: 'Experience',
};

export const pillarShadingMap = {
  [MaterialPillar.People]: {
    main: '38761d',
    sub: 'b6d7a8',
  },
  [MaterialPillar.Partnership]: {
    main: '0b5394',
    sub: '9fc5e8',
  },
  [MaterialPillar.Planet]: {
    main: 'bf9000',
    sub: 'ffe599',
  },
  [MaterialPillar.Prosperity]: {
    main: '85200c',
    sub: 'dd7e6b',
  },
  [MaterialPillar.Principle]: {
    main: '351c75',
    sub: 'b4a7d6',
  },
};

export const topTopicShadingMap: { [index: number]: string } = {
  0: 'b4a7d6',
  1: 'd2cae9',
  2: 'eae5f5',
};

export const detailedStyles: IParagraphStyleOptions[] = [
  {
    id: 'Normal',
    name: 'Normal',
    quickFormat: true,
    run: {
      size: 24,
      font: 'Helvetica Neue',
    },
  },
  {
    id: 'Title',
    name: 'Title',
    basedOn: 'Normal',
    next: 'Normal',
    quickFormat: true,
    run: {
      color: '691472',
      size: 80,
      font: 'HELVETICA NEUE CONDENSED',
      bold: true,
    },
    paragraph: {
      spacing: {
        before: 300,
        after: 200,
      },
    },
  },
  {
    id: 'Heading1',
    name: 'Heading 1',
    basedOn: 'Normal',
    next: 'Normal',
    quickFormat: true,
    run: {
      color: '691472',
      size: 48,
      font: 'HELVETICA NEUE CONDENSED',
      bold: true,
    },
    paragraph: {
      spacing: {
        before: 300,
        after: 200,
      },
    },
  },
  {
    id: 'Heading2',
    name: 'Heading 2',
    basedOn: 'Normal',
    next: 'Normal',
    quickFormat: true,
    run: {
      color: '691472',
      size: 40,
      font: 'HELVETICA NEUE CONDENSED',
      bold: true,
    },
    paragraph: {
      spacing: {
        before: 300,
        after: 200,
      },
    },
  },
  {
    id: 'Heading3',
    name: 'Heading 3',
    basedOn: 'Normal',
    next: 'Normal',
    quickFormat: true,
    run: {
      color: '691472',
      size: 26,
      font: 'HELVETICA NEUE CONDENSED',
      bold: true,
    },
    paragraph: {
      spacing: {
        before: 240,
        after: 120,
      },
    },
  },
  {
    id: 'plain',
    name: 'Plain',
    basedOn: 'Normal',
    next: 'Normal',
    quickFormat: true,
    run: {
      size: 24,
    },
  },
  {
    id: 'plainWhite',
    name: 'Plain White',
    basedOn: 'Normal',
    next: 'Normal',
    quickFormat: true,
    run: {
      size: 20,
      font: 'Arial',
      color: 'FFFFFF',
    },
  },
  {
    id: 'bold',
    name: 'Bold',
    basedOn: 'Normal',
    next: 'Normal',
    quickFormat: true,
    run: {
      size: 20,
      bold: true,
    },
  },
];

const detailedLevels: ILevelsOptions[] = [
  {
    level: BulletFormatLevel.DECIMAL,
    format: LevelFormat.DECIMAL,
    text: '%1)',
    alignment: AlignmentType.START,
    style: {
      paragraph: {
        indent: { left: 720, hanging: 390 },
      },
    },
  },
  {
    level: BulletFormatLevel.LOWER_LETTER,
    format: LevelFormat.LOWER_LETTER,
    text: '%2)',
    alignment: AlignmentType.START,
    style: {
      paragraph: {
        indent: { left: 1440, hanging: 390 },
      },
    },
  },
];

export const detailedNumberingStyles: {
  reference: string;
  levels: ILevelsOptions[];
}[] = [
  {
    reference: 'numbering-letter-one',
    levels: detailedLevels,
  },
  {
    reference: 'numbering-letter-two',
    levels: detailedLevels,
  },
  {
    reference: 'numbering-letter-three',
    levels: detailedLevels,
  },
];

export const getChart = (dataSource: DataSource) => {
  if (!dataSource.chart) {
    return paragraph('No data');
  }

  return new ImageRun({
    type: 'png',
    data: Uint8Array.from(atob(dataSource.chart), (c) => c.charCodeAt(0)),
    transformation: {
      width: dataSource.width ?? 100,
      height: dataSource.height ?? 100,
    },
  });
};
