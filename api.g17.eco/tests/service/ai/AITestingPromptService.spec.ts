/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import '../../setup'; // Import setup to configure chai-as-promised
import { expect } from 'chai';
import * as sinon from 'sinon';
import { ObjectId } from 'bson';
import { getFirstArg } from '../../utils/sinon-helpers';
import { createAIPromptTemplate } from '../../fixtures/aiTestingFixtures';
import { PromptTemplateCategory } from '../../../server/models/aiPromptTemplate';
import AITestExecution from '../../../server/models/aiTestExecution';
import { AITestingPromptService } from '../../../server/service/ai/AITestingPromptService';
import { PromptTemplateRepository } from '../../../server/service/ai/repositories/PromptTemplateRepository';
import { testLogger } from '../../factories/logger';
import ContextError from '../../../server/error/ContextError';
import BadRequestError from '../../../server/error/BadRequestError';
import { createMockWithStubs, type StubbedInstance, type MockWithStubs } from '../../utils/test-doubles';
import type { getUnifiedAIModelFactory } from '../../../server/service/ai/UnifiedAIModelFactory';

describe('AITestingPromptService', () => {
  const sandbox = sinon.createSandbox();
  let service: AITestingPromptService;
  let mockRepository: MockWithStubs<PromptTemplateRepository>;
  let mockModelFactory: MockWithStubs<ReturnType<typeof getUnifiedAIModelFactory>>;
  let mockModel: StubbedInstance<{ runCompletion: any }>;
  
  beforeEach(() => {
    // Create mock repository with all required methods as stubs
    mockRepository = createMockWithStubs<PromptTemplateRepository>({
      findAllActive: sandbox.stub(),
      findById: sandbox.stub(),
      findWithQuery: sandbox.stub(),
      findVisibleToUser: sandbox.stub(),
      create: sandbox.stub(),
      findByIdAsDocument: sandbox.stub(),
      incrementUsageCount: sandbox.stub(),
      findByIdOrFail: sandbox.stub(),
      countByQuery: sandbox.stub()
    });
    
    // Create mock model with runCompletion stub
    mockModel = {
      runCompletion: sandbox.stub().resolves({ content: 'Improved prompt text' })
    };
    
    // Create mock model factory
    mockModelFactory = createMockWithStubs<ReturnType<typeof getUnifiedAIModelFactory>>({
      getModel: sandbox.stub().returns(mockModel)
    });
    
    // Create service with mocked dependencies
    service = new AITestingPromptService(
      mockRepository.mock,
      mockModelFactory.mock,
      testLogger
    );
  });
  
  afterEach(() => {
    sandbox.restore();
  });
  
  describe('getDefaultPrompts', () => {
    it('should return default prompt templates', async () => {
      const result = await service.getDefaultPrompts();
      
      expect(result).to.be.an('array');
      expect(result.length).to.be.greaterThan(0);
      expect(result[0]).to.have.property('name');
      expect(result[0]).to.have.property('content');
      expect(result[0]).to.have.property('category');
      expect(result[0]).to.have.property('variables');
    });
  });
  
  describe('getPromptTemplates', () => {
    const testUserId = new ObjectId().toString();
    it('should return paginated prompt templates', async () => {
      const mockPrompts = [createAIPromptTemplate({
        name: 'Test Template',
        content: 'Test content {{variable}}',
        variables: ['variable']
      })];
      
      // Mock repository methods
      mockRepository.stubs.findVisibleToUser.resolves(mockPrompts);
      mockRepository.stubs.countByQuery.resolves(1);
      
      const result = await service.getPromptTemplates({
        limit: 10,
        offset: 0,
        userId: testUserId
      });
      
      // Service returns default prompts + user prompts
      expect(result.prompts).to.have.length.at.least(1);
      expect(result.total).to.be.at.least(1);
      expect(mockRepository.stubs.findVisibleToUser.calledOnce).to.be.true;
    });
    
    it('should filter by category', async () => {
      // Mock repository methods
      mockRepository.stubs.findVisibleToUser.resolves([]);
      mockRepository.stubs.countByQuery.resolves(0);
      
      await service.getPromptTemplates({
        category: PromptTemplateCategory.General,
        limit: 10,
        offset: 0,
        userId: testUserId
      });
      
      expect(mockRepository.stubs.findVisibleToUser.calledOnce).to.be.true;
      const [userId, findQuery] = mockRepository.stubs.findVisibleToUser.firstCall.args;
      expect(userId).to.equal(testUserId);
      expect(findQuery).to.deep.include({ category: PromptTemplateCategory.General });
    });
    
    it('should filter by isActive', async () => {
      mockRepository.stubs.findVisibleToUser.resolves([]);
      mockRepository.stubs.countByQuery.resolves(0);
      
      await service.getPromptTemplates({
        isActive: true,
        limit: 10,
        offset: 0,
        userId: testUserId
      });
      
      expect(mockRepository.stubs.findVisibleToUser.calledOnce).to.be.true;
      const [userId, findQuery] = mockRepository.stubs.findVisibleToUser.firstCall.args;
      expect(userId).to.equal(testUserId);
      expect(findQuery).to.deep.include({ isActive: true });
    });
    
    it('should search by name or content', async () => {
      mockRepository.stubs.findVisibleToUser.resolves([]);
      mockRepository.stubs.countByQuery.resolves(0);
      
      await service.getPromptTemplates({
        search: 'test query',
        limit: 10,
        offset: 0,
        userId: testUserId
      });
      
      expect(mockRepository.stubs.findVisibleToUser.calledOnce).to.be.true;
      const [userId, query] = mockRepository.stubs.findVisibleToUser.firstCall.args;
      expect(userId).to.equal(testUserId);
      expect(query).to.have.property('$or');
      expect(query.$or).to.have.lengthOf(2);
    });
  });
  
  describe('createPromptTemplate', () => {
    it('should create a new prompt template', async () => {
      const templateData = {
        name: 'New Template',
        content: 'Content with {{variable}}',
        category: PromptTemplateCategory.General,
        variables: {
          variable: {
            type: 'string',
            description: 'A test variable'
          }
        },
        isActive: true
      };
      
      const mockSavedTemplate = {
        _id: new ObjectId(),
        ...templateData,
        created: new Date(),
        updated: new Date(),
        createdBy: new ObjectId(),
        variables: ['variable']
      };
      
      mockRepository.stubs.create.resolves(mockSavedTemplate);
      
      const userId = new ObjectId().toString();
      const result = await service.createPromptTemplate(templateData, userId);
      
      expect(mockRepository.stubs.create.calledOnce).to.be.true;
      expect(result).to.include({
        name: templateData.name,
        content: templateData.content
      });
    });
    
    it('should extract variables from content', async () => {
      const templateData = {
        name: 'Template with variables',
        content: 'Hello {{name}}, your score is {{score}}',
        category: PromptTemplateCategory.General
      };
      
      const mockSavedTemplate = {
        _id: new ObjectId(),
        ...templateData,
        created: new Date(),
        updated: new Date(),
        createdBy: new ObjectId(),
        variables: ['name', 'score']
      };
      
      mockRepository.stubs.create.resolves(mockSavedTemplate);
      
      const userId = new ObjectId().toString();
      const result = await service.createPromptTemplate(templateData, userId);
      
      expect(mockRepository.stubs.create.called).to.be.true;
      expect(result.variables).to.have.members(['name', 'score']);
    });
  });
  
  describe('getPromptTemplate', () => {
    it('should return a specific prompt template', async () => {
      const promptId = new ObjectId();
      const mockPrompt = createAIPromptTemplate({
        _id: promptId,
        name: 'Test Template',
        content: 'Test content',
        variables: []
      });
      
      mockRepository.stubs.findById.resolves(mockPrompt);
      
      const result = await service.getPromptTemplate(promptId.toString());
      
      expect(result).to.deep.include({
        name: mockPrompt.name,
        content: mockPrompt.content,
        category: mockPrompt.category
      });
    });
    
    it('should return null if prompt not found', async () => {
      // Use a valid ObjectId so it passes validation
      const validId = new ObjectId().toString();
      mockRepository.stubs.findById.resolves(null);
      
      const result = await service.getPromptTemplate(validId);
      expect(result).to.be.null;
    });
  });
  
  describe('updatePromptTemplate', () => {
    it('should update an existing prompt template', async () => {
      const promptId = new ObjectId();
      const updateData = {
        name: 'Updated Template',
        content: 'Updated content {{newVar}}',
        isActive: false
      };
      
      const mockPrompt = createAIPromptTemplate({
        _id: promptId,
        name: 'Updated Template',
        content: 'Updated content {{newVar}}',
        isActive: false,
        variables: ['newVar']
      });
      
      // Mock the repository method
      const mockDocument = {
        ...mockPrompt,
        createdBy: new ObjectId(),
        save: sandbox.stub().resolves(mockPrompt)
      };
      mockRepository.stubs.findByIdAsDocument.resolves(mockDocument as any);
      
      const userId = mockDocument.createdBy.toString();
      const result = await service.updatePromptTemplate(promptId.toString(), updateData, userId);
      
      // Verify the result
      expect(result).to.exist;
      expect(result.name).to.equal('Updated Template');
      expect(result.content).to.equal('Updated content {{newVar}}');
      expect(result.variables).to.include('newVar');
    });
    
    it('should throw error if prompt not found', async () => {
      mockRepository.stubs.findByIdAsDocument.resolves(null);
      
      // Use a valid ObjectId so it passes validation
      const validId = new ObjectId().toString();
      const userId = new ObjectId().toString();
      await expect(service.updatePromptTemplate(validId, {}, userId))
        .to.be.rejectedWith(BadRequestError, 'Prompt template not found');
    });
    
    it('should throw error if user does not own template', async () => {
      const promptId = new ObjectId();
      const ownerId = new ObjectId();
      const otherUserId = new ObjectId().toString();
      
      const mockDocument = {
        _id: promptId,
        createdBy: ownerId,
        save: sandbox.stub()
      };
      
      mockRepository.stubs.findByIdAsDocument.resolves(mockDocument as any);
      
      await expect(service.updatePromptTemplate(promptId.toString(), {}, otherUserId))
        .to.be.rejectedWith(BadRequestError, 'You do not have permission to update this template');
    });
  });
  
  describe('deletePromptTemplate', () => {
    it('should delete a prompt template', async () => {
      const promptId = new ObjectId();
      
      const userId = new ObjectId();
      const mockDocument = {
        _id: promptId,
        createdBy: userId,
        deleteOne: sandbox.stub().resolves()
      };
      mockRepository.stubs.findByIdAsDocument.resolves(mockDocument as any);
      sandbox.stub(AITestExecution, 'countDocuments').resolves(0);
      
      const result = await service.deletePromptTemplate(promptId.toString(), userId.toString());
      
      expect(result).to.be.true;
      expect(mockDocument.deleteOne.calledOnce).to.be.true;
    });
    
    it('should throw error if prompt not found', async () => {
      mockRepository.stubs.findByIdAsDocument.resolves(null);
      
      // Use a valid ObjectId so it passes validation
      const validId = new ObjectId().toString();
      const userId = new ObjectId().toString();
      await expect(service.deletePromptTemplate(validId, userId))
        .to.be.rejectedWith(BadRequestError, 'Prompt template not found');
    });
    
    it('should throw error if user does not own template', async () => {
      const promptId = new ObjectId();
      const ownerId = new ObjectId();
      const otherUserId = new ObjectId().toString();
      
      const mockDocument = {
        _id: promptId,
        createdBy: ownerId,
        deleteOne: sandbox.stub()
      };
      
      mockRepository.stubs.findByIdAsDocument.resolves(mockDocument as any);
      
      await expect(service.deletePromptTemplate(promptId.toString(), otherUserId))
        .to.be.rejectedWith(BadRequestError, 'You do not have permission to delete this template');
    });
    
    it('should throw error if template is in use', async () => {
      const promptId = new ObjectId();
      const userId = new ObjectId();
      
      const mockDocument = {
        _id: promptId,
        createdBy: userId,
        deleteOne: sandbox.stub()
      };
      
      mockRepository.stubs.findByIdAsDocument.resolves(mockDocument as any);
      sandbox.stub(AITestExecution, 'countDocuments').resolves(5);
      
      await expect(service.deletePromptTemplate(promptId.toString(), userId.toString()))
        .to.be.rejectedWith(BadRequestError, 'Cannot delete prompt template. It is being used by 5 test execution(s).');
    });
  });
  
  describe('improvePromptTemplate', () => {
    it('should improve an existing prompt template using AI', async () => {
      const promptId = new ObjectId();
      const mockPrompt = createAIPromptTemplate({
        _id: promptId,
        name: 'Original Template',
        content: 'Simple prompt',
        variables: []
      });
      
      mockRepository.stubs.findById.resolves(mockPrompt);
      
      // Configure the mock to return the expected response
      mockModel.runCompletion.resolves({
        content: 'Improved prompt with better instructions'
      });
      
      const result = await service.improvePromptTemplate(promptId.toString());
      
      expect(result.original).to.equal(mockPrompt.content);
      expect(result.improved).to.equal('Improved prompt with better instructions');
      expect(mockModel.runCompletion.calledOnce).to.be.true;
    });
    
    it('should handle AI service errors', async () => {
      const promptId = new ObjectId();
      mockRepository.stubs.findById.resolves({ _id: promptId, content: 'Test' });
      
      // Configure the mock to reject
      mockModel.runCompletion.rejects(new Error('AI service unavailable'));
      
      await expect(service.improvePromptTemplate(promptId.toString()))
        .to.be.rejectedWith(ContextError, /Failed to improve prompt/);
    });
  });
  
  describe('generatePromptFromDescription', () => {
    it('should generate a prompt from description', async () => {
      const description = 'Create a prompt for analyzing ESG reports';
      
      // Configure the mock to return a JSON response
      mockModel.runCompletion.resolves({
        content: JSON.stringify({
          name: 'ESG Report Analysis',
          prompt: 'Analyze the following ESG report...',
          variables: ['report_content', 'focus_areas']
        })
      });
      
      const result = await service.generatePromptFromDescription(description);
      
      expect(result.name).to.equal('ESG Report Analysis');
      expect(result.prompt).to.include('ESG report');
      expect(result.variables).to.have.length(2);
    });
    
    it('should handle generation errors', async () => {
      // Configure the mock to reject
      mockModel.runCompletion.rejects(new Error('Generation failed'));
      
      await expect(service.generatePromptFromDescription('test'))
        .to.be.rejectedWith(ContextError, /Failed to generate prompt/);
    });
  });
  
  describe('improveCustomPrompt', () => {
    it('should improve a custom prompt without saving', async () => {
      const promptContent = 'Basic prompt text';
      
      // Configure the mock to return the expected response
      mockModel.runCompletion.resolves({
        content: 'Enhanced prompt with better structure'
      });
      
      const result = await service.improveCustomPrompt(promptContent);
      
      expect(result.original).to.equal(promptContent);
      expect(result.improved).to.equal('Enhanced prompt with better structure');
    });
  });
  
  // Skip testing private methods - extractVariables is tested indirectly through createPromptTemplate
});