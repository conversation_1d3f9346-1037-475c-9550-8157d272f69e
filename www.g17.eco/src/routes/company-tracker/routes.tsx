/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { RouteInterface, RoutesInterface } from '@g17eco/types/routes';
import { rootAppPath, getRootAppPath } from './utils';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { InitiativePermissions } from '@services/permissions/InitiativePermissions';
import { REPORT_EDITOR_ROUTE_MAP } from '@apps/company-tracker/routes/report-editor/ReportEditorEntryRoute';
import { SURVEY } from '@constants/terminology';

const routeDefaults = {
  auth: true,
  getRootAppPath: getRootAppPath,
  requiresInitiativeId: true,
  appPermissionId: 'app_company_tracker',
}

export const companyTrackerInitiativeRoutes: RoutesInterface = {
  NAVIGATE_BY_MAP: {
    ...routeDefaults,
    id: 'navigation_map',
    label: 'Organisational structure',
    path: `/${rootAppPath}/map/:initiativeId`,
    icon: 'fa-sitemap',
    tooltip: 'Navigation Tree of the Reporting Level Dashboards',
  },
  NAVIGATE_BY_ARCHIVED: {
    ...routeDefaults,
    id: 'archived_map',
    label: 'Archived initiatives',
    path: `/${rootAppPath}/map/:initiativeId/archived`,
    icon: 'fa-sitemap',
    tooltip: 'Navigation Tree of the Archived Reporting Level Dashboards',
  },
  SUMMARY_CURRENT: {
    ...routeDefaults,
    id: 'summary',
    label: 'Insights',
    // @TODO this seems to be broken route, should be "/:page(current)?/", but fixing breaks InsightRoute...
    path: `/${rootAppPath}/summary/:initiativeId?/:page?(current)/:summaryPage?`,
    navItemPermissionId: 'menu_summary',
    tooltip: 'Company profile dashboard',
  },
  CUSTOM_DASHBOARD: {
    ...routeDefaults,
    id: 'custom_dashboard',
    label: 'Custom Dashboard',
    // @TODO: remove surveyId since custom dashboard don't use it
    path: `/${rootAppPath}/summary/:initiativeId/:surveyId?/custom-dashboard/:dashboardId`,
    navItemPermissionId: 'menu_summary',
    requiredUserRoles: InitiativePermissions.canAccessInsightsAndDownloadsRoles,
  },
  SUMMARY: {
    ...routeDefaults,
    id: 'summary',
    label: 'Insights',
    path: `/${rootAppPath}/summary/:initiativeId?/:surveyId?/:summaryPage?`,
    navItemPermissionId: 'menu_summary',
    requiredUserRoles: InitiativePermissions.canAccessInsightsAndDownloadsRoles,
    tooltip: 'Company profile dashboard',
  },
  DOWNLOADS_CUSTOM: {
    ...routeDefaults,
    id: 'custom_downloads',
    label: 'Custom Downloads',
    path: `/${rootAppPath}/downloads/:initiativeId/custom/:action(manage|create)/:reportId?`,
    defaultParams: {
      action: 'manage',
    },
    appPermissionId: 'app_company_tracker',
    tooltip: 'Custom Downloads',
    requiresInitiativeId: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessDownloads,
  },
  DOWNLOADS_PPTX: {
    ...routeDefaults,
    id: 'pptx_downloads',
    label: 'AI Enhanced Sustainability Reports',
    tooltip: 'AI Enhanced Sustainability Reports',
    path: `/${rootAppPath}/downloads/:initiativeId/pptx-reports`,
    requiresInitiativeId: true,
    exact: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessDownloads,
  },
  DOWNLOADS: {
    ...routeDefaults,
    id: 'downloads',
    label: 'Downloads',
    path: `/${rootAppPath}/downloads/:initiativeId?/:surveyId?`,
    appPermissionId: 'app_company_tracker',
    tooltip: 'Downloads',
    requiresInitiativeId: true,
    requiredUserRoles: InitiativePermissions.canAccessInsightsAndDownloadsRoles,
    canAccessFeaturePermission: FeaturePermissions.canAccessDownloads,
  },
  MANAGE_WORKGROUPS: {
    ...routeDefaults,
    id: 'manage_workgroups',
    label: 'Manage Workgroups',
    path: `/${rootAppPath}/admin/:initiativeId/manage-users/workgroups/:workgroupId?`,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    canAccessFeaturePermission: (rootConfig) => FeaturePermissions.getLimitWorkgroups(rootConfig) > 0,
  },
  MANAGE_USERS: {
    ...routeDefaults,
    id: 'manage_users',
    label: 'Manage Users',
    path: `/${rootAppPath}/admin/:initiativeId/manage-users/:page?`,
    icon: 'fa-user',
    tooltip: 'Manage Users and Permissions',
    requiredUserRoles: InitiativePermissions.canManageRoles,
  },
  EMISSIONS_CALCULATOR_INTEGRATIONS: {
    ...routeDefaults,
    id: 'emissions_calculator_integrations',
    label: 'Emissions Calculator',
    path: `/${rootAppPath}/integrations/:initiativeId/emissions-calculator`,
    navItemPermissionId: 'menu_emissions_calculator_integrations',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    tooltip: 'Emissions Calculator',
  },
  APP_INTEGRATIONS_VIEW: {
    ...routeDefaults,
    id: 'integrations_view',
    label: 'Integration',
    path: `/${rootAppPath}/app-integrations/:initiativeId/:code`,
    navItemPermissionId: 'menu_integrations_view',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    tooltip: 'App Integrations',
  },
  APP_INTEGRATIONS: {
    ...routeDefaults,
    id: 'integrations',
    label: 'Integrations',
    path: `/${rootAppPath}/app-integrations/:initiativeId`,
    navItemPermissionId: 'menu_integrations',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    tooltip: 'App Integrations',
  },
  DATA_INTEGRATIONS: {
    ...routeDefaults,
    id: 'data_integrations',
    label: 'Data Integrations',
    path: `/${rootAppPath}/data-integrations/:initiativeId/:tab?`,
    navItemPermissionId: 'menu_data_integrations',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    tooltip: 'Data Integrations',
  },
  ...REPORT_EDITOR_ROUTE_MAP,
  CUSTOM_METRICS: {
    ...routeDefaults,
    id: 'custom_metrics',
    label: 'Custom Metrics',
    path: `/${rootAppPath}/admin/:initiativeId?/custom-metrics/:groupId?/:view?`,
    tooltip: 'Custom Metrics',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
  },
  DATA_SHARE_INITIATIVE: {
    ...routeDefaults,
    id: 'data_share_initiative',
    label: 'Data Share',
    path: `/${rootAppPath}/admin/:initiativeId/data-share/:shareId?`,
    tooltip: 'Data Share',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
  },
  REFERRALS: {
    ...routeDefaults,
    id: 'referrals',
    label: 'Referrals',
    path: `/${rootAppPath}/admin/:initiativeId/referrals`,
    tooltip: 'Referrals',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
  },
  ADMIN_DASHBOARD: {
    ...routeDefaults,
    id: 'admin_dashboard',
    label: 'Analytics Dashboard',
    path: `/${rootAppPath}/admin/:initiativeId/admin-dashboard`,
    tooltip: 'Analytics Dashboard',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    allowInitiativeChange: true,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessAdminDashboard,
  },
  SYSTEM_LOG: {
    ...routeDefaults,
    id: 'system_log',
    label: 'System Log',
    path: `/${rootAppPath}/admin/:initiativeId/system-log`,
    tooltip: 'System Log',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessAdminDashboard,
  },
  SUBSIDIARY_USER_DELEGATION: {
    ...routeDefaults,
    id: 'subsidiary_user_delegation',
    label: 'User Delegation',
    path: `/${rootAppPath}/admin/:initiativeId/user/:userId/delegation`,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessBulkDelegation,
  },
  ACCOUNT_SETTINGS: {
    ...routeDefaults,
    id: 'account_settings',
    label: 'Account Settings',
    path: `/${rootAppPath}/admin/:initiativeId/account-settings/:page?`,
    tooltip: 'Account Settings',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    defaultParams: { page: 'details' },
  },
  REPORT_SETTINGS: {
    ...routeDefaults,
    id: 'report_settings',
    label: 'Report Settings',
    path: `/${rootAppPath}/admin/:initiativeId/report-settings`,
    tooltip: 'Report Settings',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
  },
  BANKING_SETTINGS: {
    ...routeDefaults,
    id: 'banking_settings',
    label: 'Banking Settings',
    path: `/${rootAppPath}/admin/:initiativeId/banking-settings`,
    tooltip: 'Banking Settings',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
  },
  STOCK_EXCHANGE_SETTINGS: {
    ...routeDefaults,
    id: 'stock_exchange_settings',
    label: 'Stock Exchange Settings',
    path: `/${rootAppPath}/admin/:initiativeId/stock-exchange-settings`,
    tooltip: 'Stock Exchange Settings',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
  },
  DOCUMENT_LIBRARY: {
    ...routeDefaults,
    id: 'document_library',
    label: 'Document library',
    path: `/${rootAppPath}/admin/:initiativeId/document-library`,
    requiredUserRoles: InitiativePermissions.canManageRoles,
  },
  ADMIN_SETTINGS: {
    ...routeDefaults,
    id: 'admin',
    label: 'Admin',
    path: `/${rootAppPath}/admin/:initiativeId`,
    tooltip: 'Admin',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
  },
  BULK_IMPORTING: {
    ...routeDefaults,
    id: 'bulk_reporting',
    label: 'Bulk Reporting',
    path: `/${rootAppPath}/bulk-importing/:initiativeId`,
    tooltip: 'Bulk Reporting',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessBulkImporting,
  },
  SURVEY_TEMPLATES: {
    ...routeDefaults,
    id: 'survey_templates',
    label: `${SURVEY.CAPITALIZED_SINGULAR} Templates`,
    path: `/${rootAppPath}/survey-templates/:initiativeId`,
    tooltip: `${SURVEY.CAPITALIZED_SINGULAR} Templates`,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessSurveyTemplates,
  },
  SURVEY_TEMPLATES_VIEW: {
    ...routeDefaults,
    id: 'survey_templates_view',
    label: `${SURVEY.CAPITALIZED_SINGULAR} Templates`,
    path: `/${rootAppPath}/survey-templates/:initiativeId/template/:templateId/:page`,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessSurveyTemplates,
  },
  SURVEY_TEMPLATES_HISTORY: {
    ...routeDefaults,
    id: 'survey_templates_history',
    label: `${SURVEY.CAPITALIZED_SINGULAR} Templates History`,
    path: `/${rootAppPath}/survey-templates/:initiativeId/template/:templateId/history/:historyId/:page?`,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessSurveyTemplates,
  },
};

// Do not require initiative
const companytrackerRouteEntry: RouteInterface = {
  id: 'company_tracker_root',
  label: SURVEY.CAPITALIZED_SINGULAR,
  tooltip:  SURVEY.CAPITALIZED_SINGULAR,
  path: `/${rootAppPath}`,
  icon: 'fa-tasks',
  appPermissionId: 'app_company_tracker',
  exact: false,
  auth: true,
  getRootAppPath: getRootAppPath,
};

export const companyTrackerReportRoutes: RoutesInterface = {
  COMPANY_TRACKER_SURVEY_REDIRECTOR: {
    id: 'company_tracker_survey_redirector',
    label: `${SURVEY.CAPITALIZED_SINGULAR} Redirect`,
    path: `/${rootAppPath}/redirect/:initiativeId/:destination(survey)`,
    defaultParams: {
      destination: 'survey'
    },
    appPermissionId: 'app_company_tracker',
    auth: true,
    hideOnMissingPermission: true,
    requiresInitiativeId: true,
    parentId: 'company_tracker',
    getRootAppPath: getRootAppPath
  },
  COMPANY_TRACKER_LIST: {
    id: 'company_tracker_list',
    label: `All ${SURVEY.PLURAL}`,
    path: `/${rootAppPath}/reports/:initiativeId?`,
    icon: 'fa-tasks',
    appPermissionId: 'app_company_tracker',
    auth: true,
    exact: true,
    parentId: 'company_tracker',
    hideOnMissingPermission: true,
    getRootAppPath: getRootAppPath
  },
  COMPANY_TRACKER_ASSURANCE: {
    id: 'company_tracker_survey_assurance',
    label: 'Assurance',
    path: `/${rootAppPath}/reports/:initiativeId/:surveyId/assurance/:assuranceId?`,
    appPermissionId: 'app_company_tracker',
    auth: true,
    hideOnMissingPermission: true,
    requiresInitiativeId: true,
    parentId: 'company_tracker',
    getRootAppPath: getRootAppPath
  },
  COMPANY_TRACKER_SURVEY: {
    id: 'company_tracker_survey',
    label: SURVEY.CAPITALIZED_ADJECTIVE,
    path: `/${rootAppPath}/reports/:initiativeId/:surveyId/:page?`,
    defaultParams: {
      page: 'overview'
    },
    appPermissionId: 'app_company_tracker',
    auth: true,
    hideOnMissingPermission: true,
    requiresInitiativeId: true,
    parentId: 'company_tracker',
    getRootAppPath: getRootAppPath
  },
}

export const companyTrackerRoutes: RoutesInterface = {
  COMPANY_TRACKER: companytrackerRouteEntry,
  ...companyTrackerReportRoutes,
  ...companyTrackerInitiativeRoutes,
}
