import { generateScopeGroups } from './utils';
import { heading2, heading3 } from './document-structure';
import * as G17EcoCore from '@g17eco/core';
import { UtrvStatus } from '@constants/status';
import { VisibilityStatus } from '@g17eco/types/download';
import { ScopeGroupHistoricalData } from './types';
import { blueprintDefaultUnitConfig } from '@g17eco/types/survey';
import { ScopeGroups } from '@g17eco/types/surveyCommon';
import { createUtr } from '@fixtures/utr/utrv-factory';
import { faker } from '@faker-js/faker';
import { HistoricalReportData } from '@g17eco/types/reportData';

const baseScopeGroupData = {
  code: 'test',
  checked: true,
  name: 'Test',
  type: ScopeGroups.Frameworks,
};

const baseScopeGroupParams = {
  targetMap: new Map(),
  visibilityStatus: VisibilityStatus.Include,
  displayUserInput: false,
};

describe('generateScopeGroups', () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should return empty array with empty scopeGroupData', () => {
    const params = {
      ...baseScopeGroupParams,
      scopeGroupData: [],
    };
    expect(generateScopeGroups(params)).toEqual([]);
  });

  it('should return empty array with no latestHistorical data', () => {
    const params = {
      ...baseScopeGroupParams,
      scopeGroupData: [
        {
          ...baseScopeGroupData,
          questionData: [] as HistoricalReportData[],
        },
      ],
    };
    expect(generateScopeGroups(params)).toEqual([]);
  });

  it('should generate report data without subGroups', () => {
    const historicalData: ScopeGroupHistoricalData[] = [
      {
        ...baseScopeGroupData,
        questionData: [
          {
            effectiveDate: '2024-11-25T03:33:21.929Z',
            unitConfig: blueprintDefaultUnitConfig,
            reportData: [
              {
                _id: faker.database.mongodbObjectId(),
                status: UtrvStatus.Verified,
                effectiveDate: '2024-11-25T03:33:21.929Z',
                universalTracker: createUtr('test'),
              },
            ],
          },
        ] as HistoricalReportData[],
      },
    ];

    const params = {
      scopeGroupData: historicalData,
      targetMap: new Map(),
      visibilityStatus: VisibilityStatus.Include,
      displayUserInput: false,
    };
    const result = generateScopeGroups(params);

    expect(result.length).toBeGreaterThan(0);

    // Check that the first result has the same constructor as a heading2
    const expectedHeading2 = heading2(historicalData[0].name);
    expect(result[0].constructor.name).toBe(expectedHeading2.constructor.name);
  });

  it('should generate report data with subGroups', () => {
    const subGroups = [
      {
        code: 'subGroup1',
        name: 'Sub group 1',
      },
      {
        code: 'subGroup2',
        name: 'Sub group 2',
      },
    ];

    vi.spyOn(G17EcoCore, 'getGroup').mockReturnValue({
      code: 'test-group',
      name: 'Test group',
      subgroups: subGroups,
    });

    const scopeGroupData = [
      {
        code: 'test',
        checked: true,
        name: 'Test',
        type: ScopeGroups.Frameworks,
        questionData: [
          {
            effectiveDate: '2024-11-25T03:33:21.929Z',
            unitConfig: blueprintDefaultUnitConfig,
            reportData: [
              {
                _id: faker.database.mongodbObjectId(),
                status: UtrvStatus.Verified,
                effectiveDate: '2024-11-25T03:33:21.929Z',
                universalTracker: createUtr('test', { typeTags: [subGroups[1].code] }),
              },
            ],
          },
        ],
      },
    ];

    const params = {
      scopeGroupData,
      targetMap: new Map(),
      visibilityStatus: VisibilityStatus.Include,
      displayUserInput: false,
    };

    const result = generateScopeGroups(params);
    expect(result.length).toBeGreaterThan(0);

    // Check that we have the expected structure - first should be a heading2, second should be heading3
    // We'll check by comparing with newly created heading objects
    const expectedHeading2 = heading2(scopeGroupData[0].name);
    const expectedHeading3 = heading3(subGroups[1].name);

    // Check that the results have the same constructor and text content
    expect(result[0].constructor.name).toBe(expectedHeading2.constructor.name);
    expect(result[1].constructor.name).toBe(expectedHeading3.constructor.name);
  });

  it('should include headerText as heading 2 and downsize group name to heading 3', () => {
    const historicalData: ScopeGroupHistoricalData[] = [
      {
        code: 'test',
        checked: true,
        name: 'Test',
        type: ScopeGroups.Frameworks,
        questionData: [
          {
            effectiveDate: '2024-11-25T03:33:21.929Z',
            unitConfig: blueprintDefaultUnitConfig,
            reportData: [
              {
                _id: faker.database.mongodbObjectId(),
                status: UtrvStatus.Verified,
                effectiveDate: '2024-11-25T03:33:21.929Z',
                universalTracker: createUtr('test'),
              },
            ],
          },
        ],
      },
    ];

    const params = {
      scopeGroupData: historicalData,
      targetMap: new Map(),
      visibilityStatus: VisibilityStatus.Include,
      displayUserInput: false,
      headerText: 'Test Header',
    };
    const result = generateScopeGroups(params);
    expect(result.length).toBeGreaterThan(0);

    // Check that we have the expected structure - first should be a heading2, second should be heading3
    // We'll check by comparing with newly created heading objects
    const expectedHeading2 = heading2('Test Header');
    const expectedHeading3 = heading3(historicalData[0].name);

    // Check that the results have the same constructor and text content
    expect(result[0].constructor.name).toBe(expectedHeading2.constructor.name);
    expect(result[1].constructor.name).toBe(expectedHeading3.constructor.name);
  });
});
