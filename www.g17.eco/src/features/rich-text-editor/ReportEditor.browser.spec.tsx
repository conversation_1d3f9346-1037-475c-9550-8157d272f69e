/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import '@fortawesome/fontawesome-pro/css/all.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import { describe, expect, it } from 'vitest';
import { render } from 'vitest-browser-react';
import { ReportEditor } from './ReportEditor';
import { reduxFixtureStore } from '@fixtures/redux-store';
import { Provider } from 'react-redux';
import { page, userEvent } from '@vitest/browser/context';
import { html, prepareHTMLCompare } from '@fixtures/prettier';
import { ExternalDefinitionItem } from '@g17eco/types/utr-external-mapping';
import { moveLeft, selectAll, selectCharacters, sleep } from '@browser/keyboardShortcuts';

describe('ReportEditor Browser', async () => {
  const createMapping = (m: Partial<ExternalDefinitionItem>) => {
    return {
      mappingCode: 'test',
      type: 'test',
      name: 'test',
      references: [],
      utrs: [{ utrCode: 'test' }],
      ...m,
    } satisfies ExternalDefinitionItem;
  };

  const simpleMapping = (name: string) =>
    createMapping({
      mappingCode: name,
      name,
    });

  const testTag = simpleMapping('test');
  const wrapperTag = simpleMapping('wrapper-test');
  const anotherTag = simpleMapping('another-test');

  const externalMappings = { mappings: [testTag, wrapperTag, anotherTag] };
  const mockFn = () => {};

  const store = reduxFixtureStore();

  const Wrapper = () => {
    return (
      <Provider store={store}>
        <ReportEditor
          documentId='123'
          debug={false}
          externalMappings={externalMappings}
          username={'test-one'}
          editorStateJson={undefined}
          initialEditorState={undefined}
          handleLoadTemplate={mockFn}
          handleDownload={mockFn}
        />
      </Provider>
    );
  };

  const Span = (text: string) => `<span data-lexical-text='true'>${text}</span>`;

  const ixbrlTagWrapper = ({
    content,
    name = 'test',
    counter = 2,
    dir = 'ltr',
    element = 'span',
  }: {
    content: string;
    name?: string;
    counter?: number;
    dir?: string;
    element?: 'span' | 'div';
  }) => {
    const direction = dir ? `dir="${dir}"` : '';
    return `<${element} xbrltag="ix:nonFraction"
          unitref="u-${counter}"
          contextref="c-${counter}"
          factid="fact-${counter}"
          title="${name}"
          xbrlname="${name}"
          ${direction}>
        ${content}
      </${element}>`;
  };

  const applyTagOnSelection = async (optionName = 'test') => {
    await page.getByLabelText('Insert Tag').click();

    const select = page.getByRole('combobox');
    await expect.element(select).toBeInTheDocument();
    await userEvent.click(select);

    const option = page.getByText(optionName, { exact: true });
    await expect.element(option, { timeout: 500 }).toBeInTheDocument();
    await userEvent.click(option);

    const applyBtn = page.getByRole('button', { name: 'Apply Tag' });
    await expect.element(applyBtn).not.toBeDisabled();
    await applyBtn.click();
  };

  const getInlineHtml = (childNode: string) => {
    return prepareHTMLCompare(html` <p class="editor-paragraph ltr" dir="ltr">${childNode}</p> `);
  };

  it('should render content area', async () => {
    render(<Wrapper />);
    await expect.element(page.getByTestId('report-editor')).toBeInTheDocument();

    const input = page.getByTestId('report-editor-input');
    await expect.element(input).toBeInTheDocument();
    await userEvent.type(input, 'Hello world.');
    await userEvent.keyboard('{Enter}');
    await userEvent.type(input, 'hello world again');

    const expected = await prepareHTMLCompare(html`
      <p class="editor-paragraph ltr" dir="ltr">
        <span data-lexical-text="true">Hello world.</span>
      </p>
      <p class="editor-paragraph ltr" dir="ltr">
        <span data-lexical-text="true"> hello world again </span>
      </p>
    `);
    const actual = await prepareHTMLCompare(input.element().innerHTML);

    expect(actual, 'innerHTML of contenteditable did not match').toEqual(expected);
  });

  it('should render tag sidebar', async () => {
    render(<Wrapper />);
    const input = page.getByTestId('report-editor-input');
    const device = userEvent.setup();
    const inputText = 'Hello AAA';
    await device.type(input, inputText);
    await moveLeft(device, inputText.length);

    await selectCharacters(device, 'right', 5);
    await sleep(500);

    await applyTagOnSelection();

    const childNode = '<span data-lexical-text="true">Hello</span>';
    const expected = await prepareHTMLCompare(html`
      <p class="editor-paragraph ltr" dir="ltr">
        ${ixbrlTagWrapper({ content: childNode })}
        <span data-lexical-text="true"> AAA</span>
      </p>
    `);

    const actual = await prepareHTMLCompare(input.element().innerHTML);
    expect(actual, 'innerHTML of contenteditable did not match').toEqual(expected);
  });

  it('should allow to select all text and wrap', async () => {
    render(<Wrapper />);
    const input = page.getByTestId('report-editor-input');
    await userEvent.type(input, 'Hello Tag');

    await selectAll(userEvent);
    await applyTagOnSelection();

    const childNode = '<span data-lexical-text="true">Hello Tag</span>';
    const expected = await prepareHTMLCompare(html`
      <p class="editor-paragraph ltr" dir="ltr">${ixbrlTagWrapper({ content: childNode })}</p>
    `);
    const actual = await prepareHTMLCompare(input.element().innerHTML);

    expect(actual, 'innerHTML of contenteditable did not match').toEqual(expected);
  });

  it('should allow to select all text, apply tag and continue writing outside', async () => {
    render(<Wrapper />);
    const input = page.getByTestId('report-editor-input');
    await userEvent.type(input, 'Hello');

    await selectCharacters(userEvent, 'left', 1);
    await applyTagOnSelection();

    const ixbrlTag = ixbrlTagWrapper({ content: '<span data-lexical-text="true">o</span>' });

    const expected = await getInlineHtml(`<span data-lexical-text="true">Hell</span>${ixbrlTag}`);
    const actual = await prepareHTMLCompare(input.element().innerHTML);
    expect(actual, 'innerHTML of contenteditable did not match').toEqual(expected);

    await userEvent.keyboard('{ArrowRight}');
    await userEvent.type(input, ' tag');
    const updatedTag = await getInlineHtml(`
      <span data-lexical-text="true">Hell</span>
      ${ixbrlTag}
      <span data-lexical-text="true">tag</span>
    `);
    const currentHtml = await prepareHTMLCompare(input.element().innerHTML);

    expect(currentHtml, 'innerHTML of contenteditable did not match').toEqual(updatedTag);
  });

  it('should allow to select and nest tags', async () => {
    render(<Wrapper />);
    const input = page.getByTestId('report-editor-input');
    await userEvent.type(input, 'Wrapper text - Hello Tag');

    await userEvent.keyboard('{ArrowLeft}');
    await selectCharacters(userEvent, 'left', 2);
    await applyTagOnSelection(testTag.name);

    await selectCharacters(userEvent, 'left', 'Hello Ta'.length);
    await applyTagOnSelection(wrapperTag.name);

    const innerTag = ixbrlTagWrapper({
      content: '<span data-lexical-text="true">Ta</span>',
      name: testTag.name,
      counter: 2,
    });

    const updatedTag = await getInlineHtml(`
      <span data-lexical-text="true">Wrapper text -</span>
        ${ixbrlTagWrapper({
          content: `
          <span data-lexical-text="true">Hello</span>
          ${innerTag}
        `,
          name: wrapperTag.name,
          counter: 3,
        })}
      <span data-lexical-text="true">g</span>
    `);
    const currentHtml = await prepareHTMLCompare(input.element().innerHTML);

    expect(currentHtml, 'innerHTML of contenteditable did not match').toEqual(updatedTag);
  });

  it('should allow to select and nest tags with full text', async () => {
    render(<Wrapper />);
    const input = page.getByTestId('report-editor-input');
    await userEvent.type(input, 'Wrapper text - Hello Tag');

    await userEvent.keyboard('{ArrowLeft}');
    await selectCharacters(userEvent, 'left', 2);
    await applyTagOnSelection(testTag.name);

    await userEvent.keyboard('{ArrowRight}');
    await selectCharacters(userEvent, 'left', 'Hello Tag'.length);
    await applyTagOnSelection(wrapperTag.name);

    const innerTag = ixbrlTagWrapper({
      content: '<span data-lexical-text="true">Ta</span>',
      name: testTag.name,
      counter: 2,
    });

    const updatedTag = await getInlineHtml(`
      <span data-lexical-text="true">Wrapper text -</span>
        ${ixbrlTagWrapper({
          content: `
          <span data-lexical-text="true">Hello</span>
          ${innerTag}
          <span data-lexical-text="true">g</span>
        `,
          name: wrapperTag.name,
          counter: 3,
        })}
    `);
    const currentHtml = await prepareHTMLCompare(input.element().innerHTML);

    expect(currentHtml, 'innerHTML of contenteditable did not match').toEqual(updatedTag);
  });

  const applyFirstTag = async (textContent = 'Tag me twice') => {
    render(<Wrapper />);
    const input = page.getByTestId('report-editor-input');
    await userEvent.type(input, textContent);

    // Select all content and apply first tag (test tag)
    await selectAll(userEvent);
    await applyTagOnSelection(testTag.name);

    // Verify first tag was applied correctly
    const firstTagged = ixbrlTagWrapper({
      content: `<span data-lexical-text="true">${textContent}</span>`,
      name: testTag.name,
      counter: 2,
    });
    const firstExpected = await getInlineHtml(firstTagged);
    const firstActual = await prepareHTMLCompare(input.element().innerHTML);
    expect(firstActual, 'First tag not applied correctly').toEqual(firstExpected);
    return { input, firstTagged };
  };

  it('should allow wrapping same text with two different xbrl nodes twice', async () => {
    const { input } = await applyFirstTag();

    await selectAll(userEvent);
    await applyTagOnSelection(anotherTag.name);

    // Verify second tag was applied correctly (nested)
    const nestedTags = ixbrlTagWrapper({
      content: ixbrlTagWrapper({ content: Span('Tag me twice'), name: anotherTag.name, counter: 3 }),
      name: testTag.name,
      counter: 2,
    });

    const finalExpected = await getInlineHtml(nestedTags);
    const finalActual = await prepareHTMLCompare(input.element().innerHTML);

    expect(finalActual, 'Second tag not applied correctly').toEqual(finalExpected);
  });

  it('should allow wrapping same text with two different xbrl nodes, partial text', async () => {
    const { input } = await applyFirstTag();

    await selectCharacters(userEvent, 'left', 2);
    // Apply a different tag (second-test tag)
    await applyTagOnSelection(anotherTag.name);

    // Verify second tag was applied correctly (nested)
    const outer = ixbrlTagWrapper({
      content: `${Span('Tag me twi')}${ixbrlTagWrapper({ content: Span('ce'), name: anotherTag.name, counter: 3 })}`,
      name: testTag.name,
      counter: 2,
    });

    const finalExpected = await getInlineHtml(outer);
    const finalActual = await prepareHTMLCompare(input.element().innerHTML);

    expect(finalActual, 'Second tag not applied correctly').toEqual(finalExpected);
  });

  it('should allow to remove tag', async () => {
    render(<Wrapper />);
    const input = page.getByTestId('report-editor-input');
    const text = 'Hello Tag';
    await userEvent.type(input, text);
    await selectCharacters(userEvent, 'left', text.length);
    await applyTagOnSelection(testTag.name);

    const wrapperHtml = ixbrlTagWrapper({
      content: `<span data-lexical-text="true">${text}</span>`,
      name: testTag.name,
    });

    const updatedTag = await getInlineHtml(wrapperHtml);
    const currentHtml = await prepareHTMLCompare(input.element().innerHTML);
    expect(currentHtml).toEqual(updatedTag);

    await selectCharacters(userEvent, 'left', 1);
    await page.getByLabelText('Insert Tag').click();

    // Find checkbox and uncheck it
    const iconBtn = page.getByLabelText('Remove tag');
    await expect.element(iconBtn).toBeInTheDocument();
    await iconBtn.click();

    // No longer tag
    expect(await getInlineHtml(`<span data-lexical-text="true">${text}</span>`)).toEqual(
      await prepareHTMLCompare(input.element().innerHTML),
    );
  });

  it('should handle multiple nodes of blocks (paragraph, list, etc.) wrapping without infinite loop', async () => {
    render(<Wrapper />);
    const listButton = page.getByTestId('toolbar-unorder-list');
    const input = page.getByTestId('report-editor-input');

    await userEvent.type(input, 'List item 1');
    await listButton.click();
    await userEvent.keyboard('{Enter}');
    await userEvent.type(input, 'List item 2');

    // 2 enters to end the list and go to paragraph
    await userEvent.keyboard('{Enter}');
    await userEvent.keyboard('{Enter}');

    // Type some text with multiple paragraphs to create different nodes
    await userEvent.type(input, 'First paragraph');
    await userEvent.keyboard('{Enter}');
    await userEvent.type(input, 'Second paragraph');
    await userEvent.keyboard('{Enter}');
    await userEvent.type(input, 'Third paragraph');

    // Select all content
    await selectAll(userEvent);

    // Apply tag to all content
    await applyTagOnSelection(testTag.name);

    // Verify the content is wrapped correctly
    const expected = await prepareHTMLCompare(html`
      ${ixbrlTagWrapper({
        content: `
        <ul class="editor-list-ul">
          <li value="1" class="editor-listitem ltr" dir="ltr">
            <span data-lexical-text="true">List item 1</span>
          </li>
          <li value="2" class="editor-listitem ltr" dir="ltr">
            <span data-lexical-text="true">List item 2</span>
          </li>
        </ul>
      `,
        name: testTag.name,
        counter: 2,
        dir: '',
        element: 'div',
      })}
      <p class="editor-paragraph ltr" dir="ltr">
        ${ixbrlTagWrapper({
          content: `
          <span data-lexical-text="true">First paragraph</span>
        `,
          name: testTag.name,
          counter: 2,
        })}
      </p>
      <p class="editor-paragraph ltr" dir="ltr">
        ${ixbrlTagWrapper({
          content: `
          <span data-lexical-text="true">Second paragraph</span>
        `,
          name: testTag.name,
          counter: 2,
        })}
      </p>
      <p class="editor-paragraph ltr" dir="ltr">
        ${ixbrlTagWrapper({
          content: `
          <span data-lexical-text="true">Third paragraph</span>
        `,
          name: testTag.name,
          counter: 2,
        })}
      </p>
    `);

    const actual = await prepareHTMLCompare(input.element().innerHTML);
    expect(actual).toEqual(expected);

    // Try to apply another tag to the same content
    await input.click();
    await selectAll(userEvent);
    await applyTagOnSelection(anotherTag.name);

    // Verify nested tags
    const nestedExpected = await prepareHTMLCompare(html`
      ${ixbrlTagWrapper({
        content: `
        <ul class="editor-list-ul">
          <li value="1" class="editor-listitem ltr" dir="ltr">
            <span data-lexical-text="true">List item 1</span>
          </li>
          <li value="2" class="editor-listitem ltr" dir="ltr">
            <span data-lexical-text="true">List item 2</span>
          </li>
        </ul>
      `,
        name: testTag.name,
        counter: 2,
        dir: '',
        element: 'div',
      })}
      <p class="editor-paragraph ltr" dir="ltr">
        ${ixbrlTagWrapper({
          content: ixbrlTagWrapper({
            content: '<span data-lexical-text="true">First paragraph</span>',
            name: testTag.name,
            counter: 2,
          }),
          name: anotherTag.name,
          counter: 3,
          dir: '',
        })}
      </p>
      <p class="editor-paragraph ltr" dir="ltr">
        ${ixbrlTagWrapper({
          content: ixbrlTagWrapper({
            content: '<span data-lexical-text="true">Second paragraph</span>',
            name: testTag.name,
            counter: 2,
          }),
          name: anotherTag.name,
          counter: 3,
          dir: '',
        })}
      </p>
      <p class="editor-paragraph ltr" dir="ltr">
        ${ixbrlTagWrapper({
          content: ixbrlTagWrapper({
            content: '<span data-lexical-text="true">Third paragraph</span>',
            name: testTag.name,
            counter: 2,
          }),
          name: anotherTag.name,
          counter: 3,
          dir: '',
        })}
      </p>
    `);

    const nestedActual = await prepareHTMLCompare(input.element().innerHTML);
    expect(nestedActual).toEqual(nestedExpected);
  });
});
