/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { customDateFormat, DateFormat } from '../../../../util/date';
import { type AiService } from '../../../ai/service';
import { getReportingDateStart } from '../../../../util/survey';
import { type PPTXTemplateSurveyCache } from '../PPTXTemplateSurveyCache';

export class PPTXAIBuilder {
  private dialogue: string[] = [];
  private furtherExplanation: string[] = [];
  private config: {
    min: number | null;
    max: number | null;
    narrative: boolean;
    positivity: boolean;
    firstPerson: boolean;
  } = {
      min: null,
      max: null,
      narrative: false,
      positivity: false,
      firstPerson: false
    };

  public constructor(
    private aiService: AiService,
    private surveyCache: PPTXTemplateSurveyCache | undefined,
    private debugMode: boolean = false
  ) { }

  public ask(text: string | string[]): PPTXAIBuilder {
    if (Array.isArray(text)) {
      this.dialogue.push(...text);
    } else {
      this.dialogue.push(text);
    }
    return this;
  }

  public and(text: string): PPTXAIBuilder {
    this.dialogue.push(text);
    return this;
  }

  public appendConditional(text: string | undefined | null, condition: boolean = true): PPTXAIBuilder {
    const isValidText = text !== undefined && text !== null;
    if (condition && isValidText) {
      this.dialogue.push(text);
    }
    return this;
  }

  public rewrite(text: string): PPTXAIBuilder {
    return this.ask([
      'Correct spelling mistakes, improve grammar and improve readability of the following text:',
      '',
      `"${text}"`
    ]);
  }

  public narrative(enabled: boolean = true): PPTXAIBuilder {
    this.config.narrative = enabled;
    return this;
  }

  public firstPerson(enabled: boolean = true): PPTXAIBuilder {
    this.config.firstPerson = enabled;
    return this;
  }

  public bePositive(enabled: boolean = true): PPTXAIBuilder {
    this.config.positivity = enabled;
    return this;
  }

  public addFurtherExplanation(utrCode: string) {
    this.furtherExplanation.push(utrCode);
    return this;
  }

  public addTarget(utrCode: string, columnCode?: string) {
    // @TODO
    return this;
  }

  public min(limit: number) {
    this.config.min = limit;
    return this;
  }

  public max(limit: number) {
    this.config.max = limit;
    return this;
  }

  private async execFurtherExplanation(utrCode: string) {
    const utrv = await this.surveyCache?.getUTRV(utrCode);
    if (!utrv) {
      return this;
    }

    // Skip private metrics from AI processing
    if (utrv.isPrivate) {
      return this;
    }

    const furtherExplanation = utrv.notes?.stakeholder?.note ?? utrv?.note;
    if (!furtherExplanation) {
      return this;
    }
    this.dialogue.push('');
    this.dialogue.push('Further explanation:');
    this.dialogue.push(furtherExplanation);
    return this;
  }

  private async replaceReportingDate () {
    const survey = await this.surveyCache?.getSurvey();
    if (!survey) {
      return;
    }
    const reportingDateStart = getReportingDateStart(survey);
    const reportingDateEnd = customDateFormat(survey.effectiveDate, DateFormat.MonthYear);
    this.dialogue.push(
      `Replace the text ${reportingDateEnd} with "from ${reportingDateStart} to ${reportingDateEnd}" throughout the response.`
    );
  }

  private execConfig() {
    const { min, max, narrative, positivity, firstPerson } = this.config;

    const question: string[] = [
      'Write'
    ];

    if (narrative) {
      question.push('a narrative');
      if (min || max) {
        question.push('of');
      }
    }

    if (max && min) {
      question.push(`less than ${max} and more than ${min} words`);
    } else if (max) {
      question.push(`less than ${max} words`);
    } else if (min) {
      question.push(`more than ${min} words`);
    }
    question.push('about this');

    if (positivity) {
      question.push('in a positive tone');
    }

    if (firstPerson) {
      question.push('in the first person');
    }
    this.dialogue.push(`${question.join(' ')}.`);
    return this;
  }

  public reset() {
    this.dialogue = [];
    this.furtherExplanation = [];
    this.config = {
      min: null,
      max: null,
      narrative: false,
      positivity: false,
      firstPerson: false
    }
  }

  public async exec() {
    for (const utrCode of this.furtherExplanation) {
      await this.execFurtherExplanation(utrCode);
    }

    this.execConfig();
    await this.replaceReportingDate();
    const combinedDialogue = this.dialogue.join('\n');
    if (this.debugMode) {
      return combinedDialogue;
    }
    const response = await this.aiService.getReportText({ question: combinedDialogue });
    return response?.content ?? '';
  }
}
