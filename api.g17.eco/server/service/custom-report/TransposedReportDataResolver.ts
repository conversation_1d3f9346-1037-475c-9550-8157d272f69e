import ContextError from '../../error/ContextError';
import { TableColumn, UtrValueType, AggregationMode } from '../../models/public/universalTrackerType';
import { createArrayOfNumbers } from '../../util/array';
import { isSimpleNumericColumnType } from '../../util/universal-trackers';
import { AggregatedUniversalTracker } from '../utr/aggregation/AggregatedUniversalTracker';
import { UtrvType } from '../utr/constants';
import { isTableGroupAggregation } from '../utr/utrUtil';
import { getUtrvTableProp } from '../utr/utrvUtil';
import { LoggerInterface, wwgLogger } from '../wwgLogger';
import { RecordRow } from './constants';
import { ExtractValueParams, getReportDataResolver, ProcessUtrParams, ReportDataResolver } from './ReportDataResolver';
import { checkIsTableDataSafeToUse } from './utils';

export class TransposedReportDataResolver {
  constructor(private logger: LoggerInterface, private reportDataResolver: ReportDataResolver) {}

  public processUtr(params: ProcessUtrParams): RecordRow[] {
    const { utr, columnConfig, downloadScope } = params;

    const dataPoints = this.reportDataResolver.getDataPoints({
      columnConfig,
      utr,
      downloadScope,
    });

    const rows = dataPoints.map((utrWithDataPoint) => {
      if (utr.valueType === UtrValueType.Table) {
        return this.processTable({ ...params, utr: utrWithDataPoint });
      }

      if ([UtrValueType.NumericValueList, UtrValueType.TextValueList].includes(utr.valueType)) {
        return this.reportDataResolver.processComplexValueList({ ...params, utr: utrWithDataPoint });
      }

      // All the single row values
      const row = this.processRow({ ...params, utr: utrWithDataPoint });

      return [row];
    });

    return rows.flat();
  }

  private getUtrColumnMap({
    utr,
    columnConfig = {},
  }: Pick<ProcessUtrParams, 'utr' | 'columnConfig'>) {
    const table = utr.valueValidation?.table;
    const isGroupAggregation = isTableGroupAggregation(utr);
    const tableColumns = table?.columns || [];

    const aggregatedColumns = tableColumns.filter((column) => {
      return (table?.aggregation?.columns ?? []).some((col) => col.code === column.code);
    });

    // table column aggregation is valid when aggregated columns are not empty or not all columns are grouped
    const hasValidAggregation =
      isGroupAggregation && aggregatedColumns.length !== 0 && aggregatedColumns.length !== tableColumns.length;

    if (!hasValidAggregation) {
      if (isGroupAggregation) {
        this.logger.error(
          new ContextError('Table aggregation config is not valid', {
            utrId: utr._id,
            aggregationType: table?.aggregation?.type,
            tableColumnLength: tableColumns.length,
            aggregationColumnLength: aggregatedColumns.length,
          })
        );
      }
      return {
        visibleColumns: tableColumns,
        aggregatedColumns: [],
      };
    }

    const numericColumns = aggregatedColumns.filter((col) => isSimpleNumericColumnType(col));

    if (numericColumns.length > 0) {
      this.logger.error(
        new ContextError('Table aggregation contains numeric columns', {
          utrId: utr._id,
          aggregationType: table?.aggregation?.type,
          columnCodes: numericColumns.map((col) => col.code),
        })
      );
      return {
        visibleColumns: tableColumns,
        aggregatedColumns: [],
      };
    }

    const visibleColumns = columnConfig.isAggregationByGroupExcluded
      ? tableColumns.filter((col) => aggregatedColumns.every((aggCol) => aggCol.code !== col.code))
      : tableColumns;

    return {
      visibleColumns,
      aggregatedColumns,
    };
  }

  private processTable({
    utr,
    valueListMap,
    combinedColumns,
    extraData,
    displayOptions,
    columnConfig = {},
  }: ProcessUtrParams) {
    const output: RecordRow[] = [];
    const tableColumns = utr.valueValidation?.table?.columns;

    if (!tableColumns) {
      this.logger.error(new Error(`UTR ${utr._id} valueType=${utr.valueType} is missing table configuration`));
      return [];
    }

    const utrv = utr.utrvs[0];
    const tableData = getUtrvTableProp(utrv, displayOptions?.displayUserInput);

    if (!tableData || !Array.isArray(tableData)) {
      return [];
    }

    const { aggregatedColumns, visibleColumns } = this.getUtrColumnMap({ utr, columnConfig });

    if (aggregatedColumns.length > 0 && columnConfig.isAggregationByGroupExcluded) {
      this.logger.info('Proceed to aggregate rows in table', { utrId: utr._id });
      const columnCodes = utr.valueValidation?.table?.columns?.map((col) => col.code) ?? [];
      if (columnCodes.length === 0) {
        this.logger.error(new ContextError('Missing table columns config', { utrId: utr._id }));
      }
      if (checkIsTableDataSafeToUse({ tableData, columnCodes })) {
        const aggregatedUtr = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
        aggregatedUtr.add({ ...utrv, valueData: { ...utrv.valueData, table: tableData } });
        utr.maxRows = (aggregatedUtr.valueData?.table || []).length;
        utrv.valueData = aggregatedUtr.valueData;
      } else {
        this.logger.warn('Table data has mixed unit and numberScale among rows, cannot aggregate', {
          utrId: utr._id,
        });
      }
    }

    const groupedByCodes = aggregatedColumns.map((col) => col.code);
    const iterations =
      !utrv.valueData?.table || utrv.valueData.table.length === 0 ? createArrayOfNumbers(1, 1) : utrv.valueData.table;

    iterations.forEach((_row, tableRowIndex) => {
      visibleColumns.forEach((column) => {
        const extra = { ...extraData, groupedByCodes };
        const outputRow = this.processRow({
          utr,
          valueListMap,
          combinedColumns,
          extraData: extra,
          tableRowIndex,
          column,
        });
        output.push(outputRow);
      });
    });

    return output;
  }

  private processRow(
    params: Pick<ProcessUtrParams, 'utr' | 'valueListMap' | 'combinedColumns' | 'extraData'> & {
      tableRowIndex?: number;
      column?: TableColumn;
    }
  ) {
    // Grouping information
    const { utr, combinedColumns, valueListMap, extraData, tableRowIndex, column } = params;
    return combinedColumns.map(({ accessor }) => {
      return accessor({
        ...utr,
        _currentRow: { valueListMap, column, tableRowIndex },
        extraData,
      });
    });
  }

  public extractValue(params: ExtractValueParams) {
    return this.reportDataResolver.extractValue(params);
  }
}

let instance: TransposedReportDataResolver;

export const getTransposedReportDataResolver = () => {
  if (!instance) {
    instance = new TransposedReportDataResolver(wwgLogger, getReportDataResolver());
  }
  return instance;
};
