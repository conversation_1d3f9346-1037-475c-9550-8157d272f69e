/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import AIPromptTemplate, { type AIPromptTemplatePlain, type AIPromptTemplateModel } from '../../../models/aiPromptTemplate';
import type { ObjectId } from 'bson';
// Removed unused imports

/**
 * Repository for prompt template data access operations
 * Encapsulates all database operations for prompt templates
 */
export class PromptTemplateRepository {
  /**
   * Find all active prompt templates
   */
  async findAllActive(): Promise<AIPromptTemplatePlain[]> {
    return AIPromptTemplate.find({ isActive: true })
      .lean()
      .exec();
  }

  /**
   * Find templates visible to a specific user with optional filters
   * Returns user's own templates and public templates
   */
  async findVisibleToUser(
    userId: ObjectId | string,
    query: Record<string, any> = {},
    sort: Record<string, 1 | -1> = { created: -1 }
  ): Promise<AIPromptTemplatePlain[]> {
    const combinedQuery = {
      ...query,
      isActive: true,
      $or: [
        { createdBy: userId },  // User's own templates
        { isPublic: true }      // Public templates from any user
      ]
    };
    
    return AIPromptTemplate.find(combinedQuery)
      .sort(sort)
      .lean()
      .exec();
  }

  /**
   * Find a prompt template by ID
   */
  async findById(id: string): Promise<AIPromptTemplatePlain | null> {
    return AIPromptTemplate.findById(id)
      .lean()
      .exec();
  }

  /**
   * Find templates with query and pagination
   */
  async findWithQuery(query: Record<string, any>, sort: Record<string, 1 | -1> = { created: -1 }): Promise<AIPromptTemplatePlain[]> {
    return AIPromptTemplate.find(query)
      .sort(sort)
      .lean()
      .exec();
  }

  /**
   * Create a new prompt template
   */
  async create(templateData: Partial<AIPromptTemplatePlain>): Promise<AIPromptTemplateModel> {
    const template = new AIPromptTemplate(templateData);
    return template.save();
  }

  /**
   * Find template by ID as a Mongoose document (for updating)
   */
  async findByIdAsDocument(id: string): Promise<AIPromptTemplateModel | null> {
    return AIPromptTemplate.findById(id).exec();
  }

  /**
   * Increment usage count for a template
   */
  async incrementUsageCount(templateId: string): Promise<void> {
    if (templateId) {
      await AIPromptTemplate.findByIdAndUpdate(
        templateId,
        { $inc: { usageCount: 1 } }
      ).exec();
    }
  }

  /**
   * Find template by ID, throwing error if not found
   */
  async findByIdOrFail(id: string): Promise<AIPromptTemplateModel> {
    return AIPromptTemplate.findById(id).orFail().exec();
  }

  /**
   * Count total templates matching query
   */
  async countByQuery(query: Record<string, any>): Promise<number> {
    return AIPromptTemplate.countDocuments(query).exec();
  }
}