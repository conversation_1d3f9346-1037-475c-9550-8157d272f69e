import type { Handler } from 'express';
import jwt from 'jsonwebtoken';
import config from '../config';
import PermissionDeniedError from '../error/PermissionDeniedError';

/**
 * This is a way to receive requests from ws.g17.eco stack
 */
export const webSocketAuthRequired: Handler = (req, res, next) => {
  const authHeader = req.headers.authorization ?? '';
  const token = jwt.verify(authHeader, config.g17ecoWebSocket.secret);
  if (typeof token !== 'object') {
    return next(new PermissionDeniedError());
  }
  return next();
};
