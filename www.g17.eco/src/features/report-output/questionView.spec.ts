// eslint-disable-next-line no-restricted-imports
import { Paragraph, Table, TableCell, TableRow, TextRun } from 'docx';
import { renderQuestionGroup, renderQuestionValue } from './questionView';
import { ReportData } from '../../types/reportData';
import {
  numberOneWithInput,
  numberOneWithNoInput,
  numberTwoWithCurrency,
  percentageOneWithInput,
  textOneWithInput
} from '@fixtures/utr/utr-base-fixtures';
import {
  utrTableConversions,
  utrTableConversionsUtrv,
  utrTableOne,
  utrvTavbleConversionsOne
} from '@fixtures/utr/utr-table-fixtures';
import { questionObj2 } from '@fixtures/questions-fixture';
import {
  createCombined,
  createCombinedFromCode,
  createTableQuestion,
  createTableValidation
} from '@fixtures/utr/utrv-factory';
import {
  getUtrvNumericValueListOne,
  utrvNumericValueListOne,
  utrvNumericValueListOneNoInput
} from '@fixtures/utr/utr-numericValueList-fixtures';
import { NO_ANSWER_SUBMITTED } from './constants';
import { utrvTextcValueListOneNoInput, utrvTextValueListOne } from '@fixtures/utr/utr-textValueList-fixtures';
import {
  getUtrvValueList,
  utrvValueLisMultitOne,
  utrvValueListMultiOneNoInput,
  utrvValueListOne,
  utrvValueListOneNoInput
} from '@fixtures/utr/utr-ValueList-fixtures';
import { TableColumnType, UtrValueType } from '../../types/universalTracker';
import { NotApplicableTypes } from '@constants/status';
import { valueListTestTable } from '@fixtures/value-list-factory';
import { blueprintDefaultUnitConfig } from '../../types/survey';


type ListLookupArray = [string, string | undefined];
describe('questionView fn', function () {

  const textElementKey = 'w:t';
  const naText = 'Not Applicable';
  const nrText = 'Not Reporting'

  const defaultConfig = blueprintDefaultUnitConfig;
  const currency = defaultConfig.currency;

  const renderWithConfig = (question: ReportData, displayUserInput = false) => {
    return renderQuestionValue(question, defaultConfig, {}, { displayUserInput });
  };

  /** Where we expect simple values to contain data in document **/
  const getSimpleValue = (result: ReturnType<typeof renderQuestionValue>) => {
    if (typeof result !== 'object') {
      return result;
    }
    // @ts-expect-error - root is restrict property, but only way to access it for now
    const textRun = result.root.find((p: any) => p instanceof TextRun)
    // @ts-expect-error - root is restrict property, but only way to access it for now
    return textRun.root[1].root[1]
  };

  /**
   * Paragraph that will contain TextRun and Text inside
   * valueListMulti, numericValueList and textValueList
   **/
  const getValueListRows = (p: any): string[] => {
    const textRuns = p.root.filter((textRun: any) => textRun instanceof TextRun)
    return textRuns.map((textRun: any) => {
      const text = textRun.root.find((el: { rootKey: string }) => el.rootKey === textElementKey);
      return text.root[1]
    });
  }

  const getRows = (result: any) => result.root.filter((row: unknown) => row instanceof TableRow);

  const getTableColumns = (row: any) => {
    const tableColumns = row.root.filter((column: any) => column instanceof TableCell)
    return tableColumns.map((column: any) => {
      const paragraph = column.root.find((p: any) => p instanceof Paragraph)
      const textRun = paragraph.root.find((t: any) => t instanceof TextRun)
      const text = textRun.root.find((el: { rootKey: string }) => el.rootKey === textElementKey);
      return text?.root[1]
    });
  }

  describe('error situations', function () {

    test('should handle weird notApplicableType types', () => {
      const utrv = createCombinedFromCode(
        'sample',
        { valueData: { notApplicableType: 'na-legacy' } },
      );
      expect(renderWithConfig(utrv)).toEqual('')
    })

    test('should handle unsupported types', () => {
      const utrv = createCombinedFromCode(
        'sample-not-valid',
        { value: 100 },
        { valueType: 'unknown-old' }
      );
      const result = renderWithConfig(utrv) as any;
      // For unsupported types, the result should be undefined
      expect(result).toEqual(undefined);
    })

    test('NotApplicableTypes.na', () => {
      const utrv = createCombinedFromCode(
        'sample-not-valid',
        { valueData: { notApplicableType: NotApplicableTypes.na } },
      );
      const result = renderWithConfig(utrv)
      const value = getSimpleValue(result);
      expect(value).toEqual(naText)
    })


  });

  describe('renderSimpleValues', function () {


    test('number, mass unit, useInput=false', () => {
      const result = renderWithConfig(numberOneWithInput)
      const value = getSimpleValue(result);
      expect(value).toEqual(`${numberOneWithInput.value} Megajoules`)
    })

    test('number, mass unit, useInput=true, no input prop', () => {
      const result = renderWithConfig(numberOneWithNoInput, true);
      const value = getSimpleValue(result);
      expect(value).toEqual(`${numberOneWithNoInput.value} Megajoules`)
    })

    test('number, mass unit, useInput=true', () => {
      const result = renderWithConfig(numberOneWithInput, true);
      const value = getSimpleValue(result);
      expect(value).toEqual('1,000 Kilojoules')
    })

    test('currency, userInput=false', () => {
      const result = renderWithConfig(numberTwoWithCurrency)
      const value = getSimpleValue(result);
      expect(value).toEqual(`0.005 billions ${currency}`)
    })

    test('currency, userInput=true, no input', () => {
      const result = renderWithConfig(
        { ...numberTwoWithCurrency, valueData: {} },
        true
      );
      const value = getSimpleValue(result);
      expect(value).toEqual(`0.005 billions ${currency}`)
    })

    test('currency, userInput=true', () => {
      const result = renderWithConfig(numberTwoWithCurrency, true)
      const value = getSimpleValue(result);
      expect(value).toEqual(`5,000 thousands ${currency}`)
    })

    test('percentage, userInput=true', () => {
      const result = renderWithConfig(percentageOneWithInput, true)
      const value = getSimpleValue(result);
      expect(value).toEqual('1%')
    });

    test('percentage, userInput=false', () => {
      const result = renderWithConfig(percentageOneWithInput)
      const value = getSimpleValue(result);
      expect(value).toEqual('1%')
    })

    test('text, userInput=true', () => {
      const result = renderWithConfig(textOneWithInput, true)
      const value = getSimpleValue(result);
      expect(value).toEqual(textOneWithInput.valueData?.data)
    });

    test('text, userInput=false', () => {
      const result = renderWithConfig(textOneWithInput)
      const value = getSimpleValue(result);
      expect(value).toEqual(textOneWithInput.valueData?.data)
    })
  });


  describe('NumericValueList', () => {

    const valueInputData = utrvNumericValueListOne.valueData?.input?.data ?? {};
    const valueDataData = utrvNumericValueListOne.valueData?.data ?? {};

    const options = utrvNumericValueListOne.universalTracker.valueValidation?.valueList?.list ?? [];

    const generateAnswerArray = (first: string, second: string, third: string, unit: string) => {
      return [
        options[0]?.name,
        first === undefined ? '' : `${first} ${unit}`,
        options[1]?.name,
        second === undefined ? '' : `${second} ${unit}`,
        options[2]?.name,
        third === undefined ? '' : `${third} ${unit}`,
      ]
    }

    test('userInput=false', () => {
      const result = renderWithConfig(utrvNumericValueListOne)
      const value = getValueListRows(result);
      const [v1, v2, v3] = Object.values<string>(valueDataData);
      expect(value).toEqual(generateAnswerArray(v1, v2, v3, 'Kilojoules'))
    })

    test('userInput=true, no input', () => {
      const result = renderWithConfig(utrvNumericValueListOneNoInput, true)
      const value = getValueListRows(result);
      const [v1, v2, v3] = Object.values<string>(valueDataData);
      expect(value).toEqual(generateAnswerArray(v1, v2, v3, 'Kilojoules'))
    })

    test('userInput=true', () => {
      const result = renderWithConfig(utrvNumericValueListOne, true)
      const value = getValueListRows(result);
      const [v1, v2, v3] = Object.values<string>(valueInputData);
      expect(value).toEqual(generateAnswerArray(v1, v2, v3, 'Megajoules'))
    })

    test('noData, userInput=true', () => {
      const result = renderWithConfig(getUtrvNumericValueListOne({}), true)
      const value = getValueListRows(result);
      expect(value).toEqual([NO_ANSWER_SUBMITTED])
    })

    test('noData, userInput=false', () => {
      const result = renderWithConfig(getUtrvNumericValueListOne({}), false)
      const value = getValueListRows(result);
      expect(value).toEqual([NO_ANSWER_SUBMITTED])
    })
  });


  describe('TextValueList', () => {

    const utrv = utrvTextValueListOne;
    const valueDataData = utrv.valueData?.data ?? {};
    const options = utrv.universalTracker.valueValidation?.valueList?.list ?? [];

    const generateAnswerArray = (record: Record<string, any>) => {
      const answers = Object.entries<string | undefined>(record).reduce((acc, [k, v]) => {
        acc.push([k, v]);
        return acc;
      }, [] as ListLookupArray[]);

      return answers.map(([k, v]) => [options.find(o => o.code === k)?.name, v]).flat();
    }

    test('userInput=false', () => {
      const result = renderWithConfig(utrv)
      const value = getValueListRows(result);
      expect(value).toEqual(generateAnswerArray(valueDataData))
    })

    test('userInput=true, no input', () => {
      const result = renderWithConfig(utrvTextcValueListOneNoInput, true)
      const value = getValueListRows(result);
      expect(value).toEqual(generateAnswerArray(valueDataData))
    })

    test('userInput=true', () => {
      const result = renderWithConfig(utrv, true)
      const value = getValueListRows(result);
      expect(value).toEqual(generateAnswerArray(utrv.valueData?.input?.data ?? {}))
    })
  });

  describe('ValueList', () => {

    const utrv = utrvValueListOne;
    const [first] = utrv.universalTracker.valueValidation?.valueList?.list ?? [];

    test('userInput=false', () => {
      const result = renderWithConfig(utrv)
      const value = getValueListRows(result);
      expect(value).toEqual([first.name])
    })

    test('userInput=true, no input', () => {
      const result = renderWithConfig(utrvValueListOneNoInput, true)
      const value = getValueListRows(result);
      expect(value).toEqual([first.name])
    })

    test('userInput=true', () => {
      const result = renderWithConfig(utrv, true)
      const value = getValueListRows(result);
      expect(value).toEqual([first.name])
    })

    test('no data', () => {
      const noDataUtrv = getUtrvValueList({ data: '' }, UtrValueType.ValueList);
      const result = renderWithConfig(noDataUtrv, true)
      const value = getValueListRows(result);
      expect(value).toEqual([NO_ANSWER_SUBMITTED])
    })

    test('Na', () => {
      const noDataUtrv = getUtrvValueList(
        { notApplicableType: NotApplicableTypes.na },
        UtrValueType.ValueList,
      );
      const result = renderWithConfig(noDataUtrv, true)
      const value = getValueListRows(result);
      expect(value).toEqual([naText])
    })

    test('NR', () => {
      const noDataUtrv = getUtrvValueList(
        { notApplicableType: NotApplicableTypes.nr },
        UtrValueType.ValueList,
      );
      const result = renderWithConfig(noDataUtrv, true)
      const value = getValueListRows(result);
      expect(value).toEqual([nrText])
    })
  });

  describe('ValueListMulti', () => {

    const utrv = utrvValueLisMultitOne;
    const [first, second] = utrv.universalTracker.valueValidation?.valueList?.list ?? [];
    const fullAnswer = [first.name, second.name]

    test('userInput=false', () => {
      const result = renderWithConfig(utrv)
      const value = getValueListRows(result);
      expect(value).toEqual(fullAnswer)
    })

    test('userInput=true, no input', () => {
      const result = renderWithConfig(utrvValueListMultiOneNoInput, true)
      const value = getValueListRows(result);
      expect(value).toEqual(fullAnswer)
    })

    test('userInput=true', () => {
      const result = renderWithConfig(utrv, true)
      const value = getValueListRows(result);
      expect(value).toEqual(fullAnswer)
    })

    test('no data', () => {
      const noDataUtrv = getUtrvValueList({ data: '' }, UtrValueType.ValueListMulti);
      const result = renderWithConfig(noDataUtrv, true)
      const value = getValueListRows(result);
      expect(value).toEqual([NO_ANSWER_SUBMITTED])
    })
  });

  describe('get table data values', () => {


    const tableConversionHeader = utrTableConversions.valueValidation?.table?.columns.map(n => n.name) ?? [];
    const legacyOptionCode = 'old-not-available-option-code';

    test('table with partial row', () => {
      const utrv = createCombined(
        { valueData: { table: [[{ code: 'number_col2', value: 101, }]] } },
        utrTableOne,
      );
      const result = renderWithConfig(utrv) as Table
      const rows = getRows(result)
      expect(rows).toHaveLength(2);
      const [firstRow, secondRow] = rows

      const tableHeader = utrv.universalTracker.valueValidation?.table?.columns.map(n => n.name) ?? []
      expect(tableHeader).toEqual(getTableColumns(firstRow));
      // 6 columns, only second is available
      const expected = [undefined, '101', undefined, undefined, undefined, undefined];
      expect(getTableColumns(secondRow)).toEqual(expected);
    })

    test('table with partial row valueList handling', () => {
      const [first] = valueListTestTable.options;
      const utrv = createCombined(
        {
          valueData: {
            table: [
              [{ code: 'valueList_col3', value: first.code, }],
              [{ code: 'valueList_col3', value: legacyOptionCode, }],
            ]
          }
        },
        utrTableOne,
      );
      const result = renderWithConfig({
        ...utrv,
        tableValueList: [valueListTestTable]
      }) as Table
      const rows = getRows(result)
      expect(rows).toHaveLength(3);

      // 6 columns, only second is available
      const expectedRow1 = [undefined, undefined, first.name, undefined, undefined, undefined];
      expect(getTableColumns(rows[1])).toEqual(expectedRow1);
      const expectedRow2 = [undefined, undefined, legacyOptionCode, undefined, undefined, undefined];
      expect(getTableColumns(rows[2])).toEqual(expectedRow2);
    })

    test('table column with non-existant listId', () => {
      const name = 'valueList_col3-name';
      const utrv = createTableQuestion(
        { table: [[{ code: 'valueList_col3', value: legacyOptionCode, }]] },
        {
          valueValidation: createTableValidation([{
            code: 'valueList_col3',
            name: name,
            shortName: 'valueList_col3-shortName',
            listId: '12312312312312312',
            type: TableColumnType.ValueList,
          }]),
        },
      );
      const result = renderWithConfig({ ...utrv, tableValueList: [] }) as Table
      const rows = getRows(result)
      expect(rows).toHaveLength(2);
      expect(getTableColumns(rows[0])).toEqual([name]);
      expect(getTableColumns(rows[1])).toEqual([legacyOptionCode]);
    })

    test('get table values', () => {
      const result = renderQuestionValue(questionObj2) as Table
      expect(result).not.toBeNull()

      const rows = getRows(result)
      expect(rows).toHaveLength(4);
      const [firstRow, secondRow] = rows


      const tableHeader = questionObj2.universalTracker.valueValidation?.table?.columns.map(n => n.name) ?? []

      expect(tableHeader).toEqual(getTableColumns(firstRow))
      expect(getTableColumns(secondRow)).toEqual(['4', '43', 'al'])
    })


    test('get table with input and unit config', () => {
      const data = createCombinedFromCode(
        utrTableConversions.code,
        utrTableConversionsUtrv,
        utrTableConversions,
      )

      const result = renderQuestionValue(data, defaultConfig) as Table
      const rows = getRows(result);
      expect(rows).toHaveLength(3);

      const [headerRow, firstRow, secondRow] = rows

      expect(tableConversionHeader).toEqual(getTableColumns(headerRow))
      expect(getTableColumns(firstRow)).toEqual([
        '101 millions USD',
        '102 Megajoules',
        '103 Square Meters'
      ])
      expect(getTableColumns(secondRow)).toEqual([
        '201 millions USD',
        '202 Megajoules',
        '203 Square Meters'
      ])
    })

    test('get table with input and unit config and displayUserInput=true', () => {
      const result = renderQuestionValue(utrvTavbleConversionsOne, defaultConfig, {}, { displayUserInput: true }) as Table
      const rows = getRows(result);
      expect(rows).toHaveLength(3);

      const [headerRow, firstRow, secondRow] = rows

      expect(tableConversionHeader).toEqual(getTableColumns(headerRow))
      expect(getTableColumns(firstRow)).toEqual([
        '0.1 billions USD',
        '102,000 Kilojoules',
        '10,300,000 Hectares'
      ])
      expect(getTableColumns(secondRow)).toEqual([
        '0.2 billions USD',
        '202,000 Kilojoules',
        '20,300,000 Hectares'
      ])
    })

    test('no input, displayUserInput=true', () => {
      const data = createCombinedFromCode(
        utrTableConversions.code,
        { valueData: { ...utrTableConversionsUtrv.valueData, input: undefined } },
        utrTableConversions,
      )

      const result = renderQuestionValue(data, defaultConfig, {}, { displayUserInput: true }) as Table
      expect(result).toBeInstanceOf(Table)
      const rows = getRows(result);
      expect(getTableColumns(rows[1])).toEqual([
        '101 millions USD',
        '102 Megajoules',
        '103 Square Meters'
      ])
      expect(getTableColumns(rows[2])).toEqual([
        '201 millions USD',
        '202 Megajoules',
        '203 Square Meters'
      ])
    })

    test('no input, displayUserInput=true, custom unit config currency', () => {
      const data = createCombinedFromCode(
        utrTableConversions.code,
        { valueData: { ...utrTableConversionsUtrv.valueData, input: undefined } },
        utrTableConversions,
      )

      const unitConfig = { ...defaultConfig, currency: 'GBP' };

      const result = renderQuestionValue(data, unitConfig, {}, { displayUserInput: true }) as Table
      expect(result).toBeInstanceOf(Table)
      const rows = getRows(result);
      expect(getTableColumns(rows[1])).toEqual([
        `101 millions ${unitConfig.currency}`,
        '102 Megajoules',
        '103 Square Meters'
      ])
    })
  })


  describe('renderQuestionGroup', function () {

    const getParagraphText = (p: any) => p.root[1]?.root[1]?.root[1];

    test('basic questions', () => {
      const valueLabel = 'percentage-one-label';
      const utrvPercentage = createCombined(
        { value: 1, note: 'root-level-note' },
        { valueType: UtrValueType.Percentage, valueLabel },
      );
      const result = renderQuestionGroup(
        [utrvPercentage, utrvNumericValueListOne],
        'random-string-id-notused',
        defaultConfig,
        true,
      ) as any

      expect(result).toHaveLength(5);
      // [paragraphLabel, value, note]
      const [label, value, noteOutput, listLabel, listValue] = result;

      expect(getParagraphText(label)).toEqual(valueLabel);
      expect(getParagraphText(value)).toEqual('1%');
      expect(getParagraphText(noteOutput)).toEqual('Additional notes: root-level-note');
      expect(getParagraphText(listLabel)).toEqual(utrvNumericValueListOne.universalTracker.valueLabel);
      const r = getValueListRows(listValue)
      const options = utrvNumericValueListOne.universalTracker.valueValidation?.valueList?.list ?? [];
      expect(r).toEqual([
        options[0]?.name,
        '1 Megajoules',
        options[1]?.name,
        '2 Megajoules',
        options[2]?.name,
        '3 Megajoules'
      ]);
    })

    test('number and table questions', () => {
      const note = 'table simple note explain';
      const result = renderQuestionGroup(
        [numberOneWithInput, { ...utrvTavbleConversionsOne, note }],
        'random-string-id-notused',
        defaultConfig,
        true,
      )
      expect(result).toHaveLength(6);
      const [label, value, tableLabel, , tableValue, noteOutput] = result;

      expect(getParagraphText(label)).toEqual(numberOneWithInput.universalTracker.valueLabel);
      expect(getParagraphText(value)).toEqual('1,000 Kilojoules');
      expect(getParagraphText(tableLabel)).toEqual(utrvTavbleConversionsOne.universalTracker.valueLabel);

      const rows = getRows(tableValue);
      expect(rows).toHaveLength(3);

      expect(getTableColumns(rows[1])).toEqual([
        '0.1 billions USD',
        '102,000 Kilojoules',
        '10,300,000 Hectares'
      ]);
      expect(getTableColumns(rows[2])).toEqual([
        '0.2 billions USD',
        '202,000 Kilojoules',
        '20,300,000 Hectares'
      ]);

      expect(getParagraphText(noteOutput)).toEqual(`Additional notes: ${note}`);
    })
  });
});
