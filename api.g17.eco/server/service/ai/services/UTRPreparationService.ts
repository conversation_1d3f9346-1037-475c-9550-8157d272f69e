/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import type { FilterQuery } from 'mongoose';
import BadRequestError from '../../../error/BadRequestError';
import type { ExecuteTestRequest } from '../ai-testing-types';
import UniversalTracker, { type UniversalTrackerPlain } from '../../../models/universalTracker';
import type { KeysEnum } from '../../../models/commonProperties';
import type { FileSupportAiModelWithCapabilities } from '../models/FileSupportAiModel';
import { buildUTRTypeConditions } from '../utils/utr-query-builder';

// UTR projection for prompts
type UtrPromptData = Pick<
  UniversalTrackerPlain,
  'code' | 'name' | 'type' | 'alternatives' | 'instructions' | 'valueLabel' | 'valueType'
>;

const UTR_PROMPT_DATA_PROJECTION: KeysEnum<UtrPromptData, 1> = {
  code: 1,
  name: 1,
  type: 1,
  alternatives: 1,
  instructions: 1,
  valueLabel: 1,
  valueType: 1,
};

/**
 * Service responsible for preparing UTRs for AI testing
 */
export class UTRPreparationService {
  constructor(
    private utrModel: typeof UniversalTracker
  ) {}

  /**
   * Prepare UTRs based on the request criteria
   */
  async prepareUtrs(request: ExecuteTestRequest): Promise<UtrPromptData[]> {
    if (request.utrSelection && request.utrSelection.filters) {
      // Use simple MongoDB query for UTR selection
      const filters = request.utrSelection.filters;
      let mongoQuery: FilterQuery<UniversalTrackerPlain> = {};

      if (filters.codes && Array.isArray(filters.codes)) {
        mongoQuery.code = { $in: filters.codes };
      }

      if (filters.type && Array.isArray(filters.type)) {
        // Convert simple strings to TypeFilter format for compatibility
        const typeFilters = filters.type.map(t => 
          typeof t === 'string' ? { type: t } : t
        );
        const typeConditions = buildUTRTypeConditions(typeFilters);
        if (typeConditions) {
          // Merge with existing query conditions
          if (Object.keys(mongoQuery).length > 0) {
            // If we already have conditions, combine them with AND logic
            const existingConditions = { ...mongoQuery };
            mongoQuery = {
              $and: [existingConditions, typeConditions]
            };
          } else {
            // If no existing conditions, just use the type conditions
            Object.assign(mongoQuery, typeConditions);
          }
        }
      }

      const limit = filters.limit || 100;

      const utrs = await this.utrModel
        .find(mongoQuery, UTR_PROMPT_DATA_PROJECTION)
        .limit(limit)
        .lean()
        .exec() as UtrPromptData[];

      if (utrs.length === 0) {
        throw new BadRequestError('No UTRs found matching the specified criteria');
      }

      return utrs;
    }

    // No UTR selection provided - return empty array to allow document analysis without specific UTR matching
    return [];
  }

  /**
   * Create a file containing UTRs for AI processing
   */
  async createUtrsFile(utrs: UtrPromptData[], aiModel: FileSupportAiModelWithCapabilities): Promise<string> {
    const utrsContent = JSON.stringify(utrs);
    const file = new File([utrsContent], 'utrs.json', { type: 'application/json' });

    const uploadedFile = await aiModel.createFile({ file });
    return uploadedFile.id;
  }
}