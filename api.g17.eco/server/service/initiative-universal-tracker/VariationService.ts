import { ObjectId } from 'bson';
import { RootFilterQuery } from 'mongoose';
import { InitiativeUniversalTrackerPlain } from '../../models/initiativeUniversalTracker';
import { KeysEnum } from '../../models/public/projectionUtils';
import { UtrValueType, Variation, VariationDataSource } from '../../models/public/universalTrackerType';
import { ValueList } from '../../models/public/valueList';
import { UniversalTrackerPlain } from '../../models/universalTracker';
import UniversalTrackerValue, { UniversalTrackerValuePlain } from '../../models/universalTrackerValue';
import { excludeSoftDeleted } from '../../repository/aggregations';
import { generateDateMatch, getUTCEndOf, subtractDate } from '../../util/date';
import { isNumericString } from '../../util/string';
import {
  getInitiativeUniversalTrackerService,
  InitiativeUniversalTrackerService,
} from '../initiative/InitiativeUniversalTrackerService';
import { ActionList, UtrvType } from '../utr/constants';
import { getUtrvDataProp, getUtrvTableProp, getUtrvValue } from '../utr/utrvUtil';
import { SurveyModelPlain } from '../../models/survey';
import { SupportedMeasureUnits } from '../../service/units/unitTypes';
import { isMultiRowTable, isTableGroupAggregation } from '../utr/utrUtil';
import { AggregatedUniversalTracker } from '../utr/aggregation/AggregatedUniversalTracker';
import { AggregationMode } from '../../models/public/universalTrackerType';
import { RowData } from '../../models/public/universalTrackerValueType';

const displayUserInput = true;

interface ExtendedRowData extends Omit<RowData, 'code'> {
  // aggregation only support text / valueList for now, may need to pull unit / numberScale later if numeric value is supported
  aggregationColumns?: Pick<RowData, 'code' | 'value'>[];
}

type DataSourceValue = ExtendedRowData | ExtendedRowData[] | undefined;

type Utr = Pick<UniversalTrackerPlain, '_id' | 'valueType' | 'valueValidation' | 'valueAggregation'> & {
  valueListOptions?: ValueList;
};

const utrProjection: KeysEnum<Utr> = {
  _id: 1,
  valueType: 1,
  valueValidation: 1,
  valueListOptions: 1,
  valueAggregation: 1,
};

type Survey = Pick<SurveyModelPlain, '_id' | 'unitConfig'>;

const surveyProjection: KeysEnum<Survey> = { _id: 1, unitConfig: 1 };

type Utrv = UniversalTrackerValuePlain & { universalTracker: Utr };

type SourceUtrv = Utrv & { survey: Survey };

const DATA_SOURCE_DURATION_MAP = {
  [VariationDataSource.LastMonth]: 1,
  [VariationDataSource.LastYear]: 12,
};

export class VariationService {
  constructor(private initiativeUtrService: InitiativeUniversalTrackerService) {}

  public async getUtrvVariations(utrvId: string | ObjectId) {
    const utrv = await UniversalTrackerValue.findById(utrvId)
      .populate({ path: 'universalTracker', select: utrProjection, populate: [{ path: 'valueListOptions' }] })
      .orFail()
      .lean<Utrv>()
      .exec();
    const { initiativeId, universalTrackerId } = utrv;
    const match = { universalTrackerId };

    const [initiativeUtr] = await this.initiativeUtrService.getRootInitiativeUniversalTrackers(initiativeId, match);
    const result = await this.process(utrv, initiativeUtr);
    return result ?? [];
  }

  private async process(utrv: Utrv, initiativeUtr: InitiativeUniversalTrackerPlain | undefined) {
    if (!initiativeUtr || !initiativeUtr.valueValidation) {
      return [];
    }

    switch (utrv.universalTracker.valueType) {
      case UtrValueType.Number:
      case UtrValueType.Percentage: {
        const variations = initiativeUtr.valueValidation.variations;
        return this.processVariations(utrv, variations);
      }
      case UtrValueType.Table:
        return this.processTableVariations(utrv, initiativeUtr);
      case UtrValueType.NumericValueList: {
        const variations = initiativeUtr.valueValidation.variations;
        return this.processNumericValueListVariations(utrv, variations);
      }
      default:
        return;
    }
  }

  private async processVariations(utrv: Utrv, variations: Variation[] | undefined, valueListCode?: string) {
    if (!variations || variations.length === 0) {
      return [];
    }

    const results = [];
    for (const variation of variations) {
      const sourceUtrv = await this.getDataSourceUtrv(utrv, variation);
      const dataSourceValue = await this.getDataSourceValue(sourceUtrv, valueListCode);

      if (!dataSourceValue || !sourceUtrv) {
        continue;
      }

      const dataSourceValues = Array.isArray(dataSourceValue) ? dataSourceValue : [dataSourceValue];

      for (const { value, unit, numberScale, aggregationColumns } of dataSourceValues) {
        if (!isNumericString(value)) {
          continue;
        }
        const numberValue = Number(value);
        const unitType = valueListCode
          ? utrv.universalTracker.valueValidation?.table?.columns.find((col) => col.code === valueListCode)?.unitType
          : utrv.universalTracker.unitType;

        results.push({
          ...variation,
          valueListCode,
          details: {
            min: numberValue - (variation.min / 100) * numberValue,
            max: numberValue + (variation.max / 100) * numberValue,
            baseline: numberValue,
            unit: unitType === SupportedMeasureUnits.currency ? sourceUtrv.survey?.unitConfig?.currency : unit,
            numberScale,
            aggregationColumns,
            effectiveDate: sourceUtrv.effectiveDate,
          },
        });
      }
    }
    return results;
  }

  private async processTableVariations(utrv: Utrv, initiativeUtr: InitiativeUniversalTrackerPlain) {
    const utr = utrv.universalTracker;
    if (isMultiRowTable(utr) && !isTableGroupAggregation(utr)) {
      return;
    }
    const columns = initiativeUtr.valueValidation?.table?.columns;
    if (!columns || columns.length === 0) {
      return;
    }
    const results = [];
    for (const column of columns) {
      const { validation, code } = column;
      const variations = await this.processVariations(utrv, validation?.variations, code);
      results.push(...variations);
    }
    return results;
  }

  private async processNumericValueListVariations(utrv: Utrv, variations: Variation[] | undefined) {
    const options = utrv?.universalTracker?.valueListOptions?.options;
    if (!options?.length) {
      return;
    }
    const results = [];
    for (const option of options) {
      const extendedVariations = await this.processVariations(utrv, variations, option.code);
      results.push(...extendedVariations);
    }
    return results;
  }

  private async getDataSourceUtrv(utrv: Utrv, variation: Pick<Variation, 'dataSource'>) {
    const match: RootFilterQuery<UniversalTrackerValuePlain> = {
      initiativeId: utrv.initiativeId,
      universalTrackerId: utrv.universalTrackerId,
      period: utrv.period,
      // exclude targets
      type: { $in: [UtrvType.Actual, UtrvType.Baseline] },
      // exclude aggregated utrvs
      sourceItems: { $in: [null, []] },
      ...excludeSoftDeleted(),
    };

    const dataSource = variation.dataSource;

    if (dataSource === VariationDataSource.LastMonth || dataSource === VariationDataSource.LastYear) {
      const date = subtractDate(utrv.effectiveDate, DATA_SOURCE_DURATION_MAP[dataSource], 'month');
      match.effectiveDate = generateDateMatch(date);
      match.status = { $ne: ActionList.Created };
    }

    if (dataSource === VariationDataSource.LatestVerified) {
      match.status = ActionList.Verified;
      match.effectiveDate = { $lt: getUTCEndOf('month', utrv.effectiveDate) };
    }

    return UniversalTrackerValue.findOne(match, null, { sort: { effectiveDate: -1 } })
      .populate({ path: 'universalTracker', select: utrProjection, populate: [{ path: 'valueListOptions' }] })
      .populate({ path: 'survey', select: surveyProjection })
      .lean<SourceUtrv>()
      .exec();
  }

  private async getDataSourceValue(utrv: Utrv | null, valueListCode?: string): Promise<DataSourceValue> {
    if (!utrv) {
      return;
    }
    switch (utrv.universalTracker.valueType) {
      case UtrValueType.Number:
      case UtrValueType.Percentage: {
        return getUtrvValue(utrv, displayUserInput);
      }
      case UtrValueType.Table: {
        if (isMultiRowTable(utrv.universalTracker)) {
          return this.getAggregatedColumnValues(utrv, valueListCode);
        }
        return this.getInputColumnValue(utrv, valueListCode);
      }
      case UtrValueType.NumericValueList: {
        const { data, unit, numberScale } = getUtrvDataProp(utrv, displayUserInput) ?? {};
        if (!data || !valueListCode) {
          return;
        }
        return { value: data[valueListCode], unit, numberScale };
      }
      default:
        return;
    }
  }

  private getInputColumnValue = (utrv: Utrv, valueListCode?: string) => {
    const table = getUtrvTableProp(utrv, displayUserInput);
    return table?.[0].find((col) => col.code === valueListCode);
  };

  private getAggregatedColumnValues(utrv: Utrv, valueListCode?: string) {
    const utr = utrv.universalTracker;
    const tableData = getUtrvTableProp(utrv, false);
    const column = utr.valueValidation?.table?.columns.find((col) => col.code === valueListCode);

    if (!tableData?.length || !column) {
      return;
    }

    const aggregatedUtr = new AggregatedUniversalTracker(utr, [], UtrvType.Actual, AggregationMode.Combined);
    aggregatedUtr.add({ ...utrv, valueData: { ...utrv.valueData, table: tableData } });
    const aggregatedTableData = aggregatedUtr.valueData?.table ?? [];

    const groupByColumnCodes = utr.valueValidation?.table?.aggregation?.columns?.map((col) => col.code) ?? [];

    return aggregatedTableData.reduce<ExtendedRowData[]>((acc, row) => {
      const aggregatedColumn = row.find((col) => col.code === valueListCode);
      if (aggregatedColumn) {
        acc.push({
          value: aggregatedColumn.value,
          unit: column.unit,
          numberScale: column.numberScale,
          aggregationColumns: row.filter((col) => groupByColumnCodes?.includes(col.code)),
        });
      }
      return acc;
    }, []);
  }
}

let instance: VariationService;
export const getVariationService = () => {
  if (!instance) {
    instance = new VariationService(getInitiativeUniversalTrackerService());
  }
  return instance;
};
