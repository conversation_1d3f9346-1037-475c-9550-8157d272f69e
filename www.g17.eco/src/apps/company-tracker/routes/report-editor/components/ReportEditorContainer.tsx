/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { ReportEditor } from '@features/rich-text-editor/ReportEditor';
import { useProviderFactory } from '@features/rich-text-editor';
import { ReportDocument, ReportDocumentStatus } from '@g17eco/types/reportDocument';
import { DashboardRow, DashboardSection } from '@g17eco/molecules/dashboard';
import { useCurrentUser } from '@hooks/useCurrentUser';
import { getFullName } from '@utils/user';
import { useEffect, useMemo, useState } from 'react';
import { UserMin } from '@g17eco/types/user';
import { useListExternalMappingQuery } from '@api/initiative-external-mapping';
import { Button } from 'reactstrap';
import {
  useLazyGetReportDocumentTemplateQuery,
  useDownloadReportDocumentMutation,
  useInitializeReportDocumentQuery,
} from '@api/initiative-report-documents';
import { Loader } from '@g17eco/atoms/loader';
import { BasicAlert } from '@g17eco/molecules/alert';
import { ConfirmationModal } from '@g17eco/molecules/confirm-modal';
import { useToggle } from '@hooks/useToggle';
import { SerializedEditorState } from 'lexical';
import FileSaver from 'file-saver';
import { useHistory } from 'react-router-dom';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import { skipToken } from '@reduxjs/toolkit/query';

interface Props {
  reportDocument: ReportDocument;
  disabled?: boolean;
}

/** Used for polling till the lexical state is synced with API */
const shouldContinuePolling = (status: ReportDocumentStatus) =>
  status === ReportDocumentStatus.Pending || status === ReportDocumentStatus.Generated;

/** 
 * Used to determine if we should render the editor
 * - status = completed => Synced with API => fully render editor
 * - status = generated => Still polling => render editor, but disable editing
 */
const shouldRenderEditor = (status?: ReportDocumentStatus) =>
  status === ReportDocumentStatus.Completed || status === ReportDocumentStatus.Generated;

export const ReportEditorContainer = (props: Props) => {
  const {
    reportDocument: { _id: reportId, initiativeId, type, title, description, status },
    disabled = false,
  } = props;
  const { providerFactory } = useProviderFactory();
  const currentUser = useCurrentUser();
  const [openConfirm, toggleConfirm] = useToggle();
  const history = useHistory();
  const [shouldPolling, setShouldPolling] = useState(shouldContinuePolling(status));

  const onCancel = () => {
    history.push(generateUrl(ROUTES.REPORT_EDITOR_LIST, { initiativeId, type }));
  };

  const users = useMemo<UserMin[]>(() => {
    return [];
  }, []);

  const {
    data: externalMappings,
    isLoading: loadingExternalMappings,
    error: errorExternalMappings,
  } = useListExternalMappingQuery({ initiativeId, type });

  const { data: initializeState } = useInitializeReportDocumentQuery(
    shouldPolling ? {
      initiativeId,
      reportId,
    } : skipToken,
    { pollingInterval: shouldPolling ? 5000 : 0 },
  );

  const [getReportDocumentTemplate, { data: editorStateJson, isLoading: loadingTemplate, error: templateError }] =
    useLazyGetReportDocumentTemplateQuery();

  const [downloadReportDocument] = useDownloadReportDocumentMutation();

  const loadTemplate = () => {
    getReportDocumentTemplate({ reportId, initiativeId })
      .unwrap()
      .finally(() => toggleConfirm());
  };

  const handleDownload = (state: SerializedEditorState) => {
    downloadReportDocument({
      initiativeId,
      reportId,
      editorState: state,
    })
      .unwrap()
      .then((response) => {
        const blob = new Blob([response.xmlString], { type: 'application/xhtml+xml' });
        FileSaver.saveAs(blob, response.filename || 'report.xhtml');
      });
  };

  const isLoading = loadingExternalMappings || loadingTemplate;
  const error = errorExternalMappings || templateError;

  useEffect(() => {
    if (initializeState?.status) {
      setShouldPolling(shouldContinuePolling(initializeState.status));
    }
  }, [initializeState?.status]);

  return (
    <>
      {isLoading ? <Loader /> : null}
      {error ? <BasicAlert type='danger'>{error.message}</BasicAlert> : null}
      <DashboardRow>
        <Button type='button' color='link' onClick={onCancel}>
          <i className='fas fa-arrow-left mr-2'></i>
          Back
        </Button>
      </DashboardRow>
      <DashboardSection title={title}>
        <p>{description}</p>
        {shouldRenderEditor(initializeState?.status || status) ? (
          <ReportEditor
            username={getFullName(currentUser)}
            users={users}
            disabled={disabled || isLoading || shouldPolling}
            externalMappings={externalMappings}
            documentId={reportId}
            providerFactory={providerFactory}
            editorStateJson={editorStateJson}
            initialEditorState={initializeState?.lexicalState}
            handleLoadTemplate={toggleConfirm}
            handleDownload={handleDownload}
          />
        ) : (
          <Loader />
        )}
      </DashboardSection>
      <ConfirmationModal
        title='Get template'
        content='You will lose your current document data. Are you sure you want to progress this?'
        isOpen={openConfirm}
        handleCancel={toggleConfirm}
        confirmButton={
          <Button color='primary' disabled={loadingTemplate} onClick={loadTemplate}>
            Confirm
          </Button>
        }
      />
    </>
  );
};
