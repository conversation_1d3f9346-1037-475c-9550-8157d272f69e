/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { Document, Footer, Header, ImageRun, PageOrientation, Paragraph, Table, TableCell, TableRow } from 'docx';
import { SurveyInitiative } from '../../types/survey';
import {
  ArrayToTableCellType,
  cellToTableCell,
  Styles,
  getWWGLogoLandscape,
  footerLogos,
  convertImgToBase64URL,
  heading,
  heading3,
  imageWrapper,
  pagebreak,
  paragraphWithBoldText,
  spacer,
  getBorders,
} from './document-structure';
import { formatDate } from '../../utils/date';
import { ReportData } from '../../types/reportData';
import { renderQuestionValue, renderTable } from './questionView';
import { getGroup, Group, standards } from '@g17eco/core';
import { declarativeStyles } from './styles';
import { UtrValueTypes } from '../../utils/universalTracker';
import {
  createNoBorderCell,
  getLatestNote,
  getTypeCode,
  getTypeTags,
  getUtrvStatusText,
  simpleTable
} from './group-builder';
import { AppendixGenerator } from './AppendixGenerator';
import { NO_ANSWER_SUBMITTED } from './constants';
import { DownloadMultiScope } from '../../types/download';
import { QUESTION } from '@constants/terminology';

const YALE_BLUE = '1a4791';

interface DocumentGeneratorProps {
  getStyles: () => ({ paragraphStyles: typeof declarativeStyles });
  getHeading: () => Paragraph[];
  getSubHeading: () => Paragraph[];
  getTables: () => (Paragraph | Table)[];
  getAppendix: () => (Paragraph | Table)[];
  getLogos: () => Paragraph[];
  getFooters?: () => { default: Footer, first?: Footer | undefined }
  headerFirstPageOnly?: boolean;
}

interface DocumentGenerator extends DocumentGeneratorProps {
  generate: (props: DocumentGeneratorProps) => Document;
}

type CreateCellType = (cell: ArrayToTableCellType, styles?: Styles) => TableCell;

const generate = (props: DocumentGeneratorProps) => new Document({
  styles: props.getStyles(),
  sections: [
    {
      properties: {
        titlePage: true,
        page: {
          size: {
            orientation: PageOrientation.LANDSCAPE,
          }
        }
      },
      headers: {
        first: new Header({
          children: props.getLogos(),
        }),
        default: props.headerFirstPageOnly ? undefined : new Header({
          children: props.getLogos(),
        }),
      },
      footers: props.getFooters?.(),
      children: [
        ...props.getHeading(),
        ...props.getSubHeading(),
        ...props.getTables(),
        ...props.getAppendix()
      ]
    }
  ]
});

export const SimpleReportGenerator = async (params: {
  survey: SurveyInitiative;
  reportData: ReportData[];
  reportType: string;
  reportLogo: string;
  color: string;
  downloadScope: DownloadMultiScope;
}): Promise<DocumentGenerator> => {
  const { survey, reportData, reportType, reportLogo, color = YALE_BLUE, downloadScope } = params;
  const isIPIECAReport = reportType === 'ipieca';

  const clientLogoBase64 = survey.initiative.profile ? await convertImgToBase64URL(survey.initiative.profile) : undefined;

  const wwgLogoLandscape = await getWWGLogoLandscape();

  const group = getGroup('standards', reportType);
  if (!group) {
    throw new Error(`${reportType} group is not available`)
  }

  const SECTIONS = standards[reportType].subgroups ?? [];

  const logo = new ImageRun({
    type: 'png',
    data: Uint8Array.from(atob(reportLogo), c => c.charCodeAt(0)),
    transformation: {
      width: 70,
      height: 70
    }
  });

  const createBorderCell = (cell: ArrayToTableCellType, styles?: Styles) => {
    return cellToTableCell(cell, { ...styles, borders: getBorders('6C757D') });
  }

  const tableHeaderStyle = {
    shading: {
      fill: color
    },
    style: 'bold',
    textRun: {
      color: 'FFFFFF'
    }
  };

  const borderCols = [`${QUESTION.CAPITALIZED_SINGULAR} Code`, QUESTION.CAPITALIZED_SINGULAR, 'Response', 'Further Explanation / Notes'].map((title) =>
    createBorderCell(title, tableHeaderStyle)
  );

  const cols = [
    createNoBorderCell(`${group.code.toUpperCase()} Code`, tableHeaderStyle),
    createNoBorderCell('Metric name', tableHeaderStyle),
    createNoBorderCell('Response', tableHeaderStyle),
    createNoBorderCell('Note / Explain', tableHeaderStyle),
    createNoBorderCell('Reporting status', tableHeaderStyle)
  ];

  const tableHeaderRow = new TableRow({ children: isIPIECAReport ? borderCols : cols });

  const getRows = (section: Group, data: ReportData[]) => {
    return data
      .filter(d => {
        const typeTags = getTypeTags(d.universalTracker);
        if (typeTags.includes(section.code)) {
          return true;
        }
        return false;
      })
      .map(d => createRow(d, isIPIECAReport ? createBorderCell : createNoBorderCell));
  }

  const appendix = new AppendixGenerator();

  const { displayUserInput } = downloadScope;

  const getValueStr = (m: ReportData) => {
    if (m.universalTracker.valueType === UtrValueTypes.table) {
      const table = renderTable({
        question: m,
        returnEmpty: false,
        unitConfig: survey.unitConfig,
        width: 14000,
        displayUserInput,
      });
      return table ? appendix.add(m.universalTracker.name, table) : undefined;
    }
    return renderQuestionValue(m, survey.unitConfig, undefined, { displayUserInput });
  }

  const createRow = (currentData: ReportData, createCell: CreateCellType) => {
    const cols = [
      createCell(getTypeCode(currentData, reportType)),
      createCell(currentData.universalTracker.name),
      createCell(getValueStr(currentData) ?? NO_ANSWER_SUBMITTED),
      createCell(getLatestNote(currentData)),
      createCell(getUtrvStatusText(currentData) ?? '')
    ];
    return new TableRow({ children: cols });
  }

  const getStyles = () => ({
    paragraphStyles: declarativeStyles
  });

  const getLogos = () => [
    imageWrapper(logo),
    imageWrapper(wwgLogoLandscape)
  ];

  const getHeading = () => [
    heading(`${group.name} Aligned Report`)
  ];

  const getSubHeading = () => [
    paragraphWithBoldText('Company Name: ', survey.initiative.name),
    paragraphWithBoldText('Company Sector: ', survey.initiative.industryText ?? ''),
    paragraphWithBoldText('Report Date: ', formatDate(survey.effectiveDate, 'MMMM YYYY')),
  ];

  const getFooters = () => footerLogos(reportLogo, clientLogoBase64);

  const getColWidths = () => {
    if (isIPIECAReport) {
      return [2000, 3500, 5000, 3500];
    }
    return [2000, 2000, 6000, 2000, 2000];
  };

  const getTables = () => (
    SECTIONS.map((section, i) => {
      const rows = getRows(section, reportData);
      if (rows.length === 0) {
        return [];
      }
      return [
        i === 0 ? spacer() : pagebreak(),
        heading3(section.name, {
          textRun: {
            color: color
          }
        }),
        simpleTable([tableHeaderRow, ...rows], 14000, getColWidths()),
      ]
    }).flat()
  );

  const getAppendix = () => appendix.render();

  return {
    generate,
    getStyles,
    getLogos,
    getHeading,
    getSubHeading,
    getTables,
    getAppendix,
    getFooters,
    headerFirstPageOnly: isIPIECAReport,
  }
}
