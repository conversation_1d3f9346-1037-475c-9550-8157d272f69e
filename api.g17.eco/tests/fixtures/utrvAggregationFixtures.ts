/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { UtrValueType, ValueAggregation } from '../../server/models/public/universalTrackerType';
import { AggregationMode } from '../../server/service/utr/aggregation/../../../models/public/universalTrackerType';

const universalTrackerId = new ObjectId();
const aggregatedSumUniversalTrackerId = new ObjectId();
const aggregatedAvgUniversalTrackerId = new ObjectId();
const aggregatedLatestUniversalTrackerId = new ObjectId();

export const mockPercentageUtr = {
  _id: universalTrackerId,
  idString: `${universalTrackerId}`,
  code: 'code1',
  valueType: UtrValueType.Percentage
};
export const mockSumAggregatedUtr = {
  _id: aggregatedSumUniversalTrackerId,
  idString: `${aggregatedSumUniversalTrackerId}`,
  code: 'code2',
  valueType: UtrValueType.Number,
  valueAggregation: ValueAggregation.ValueSumAggregator
};
export const mockAverageAggregatedUtr = {
  _id: aggregatedAvgUniversalTrackerId,
  idString: `${aggregatedAvgUniversalTrackerId}`,
  code: 'code3',
  valueType: UtrValueType.Number,
  valueAggregation: ValueAggregation.ValueAverageAggregator
};
export const mockLatestAggregatedUtr = {
  _id: aggregatedLatestUniversalTrackerId,
  idString: `${aggregatedLatestUniversalTrackerId}`,
  code: 'code4',
  valueType: UtrValueType.Number,
  valueAggregation: ValueAggregation.LatestAggregator
};

const initiativeIdMap = new Map<number, ObjectId>();
export const initiativeIdMapper = (friendlyId: number) => {
  if (!initiativeIdMap.has(friendlyId)) {
    initiativeIdMap.set(friendlyId, new ObjectId());
  }
  return initiativeIdMap.get(friendlyId);
};

const createInitiative = (initiativeNum: number, parentId?: number) => {
  const initiativeId = initiativeIdMapper(initiativeNum);
  const newParentId = parentId === undefined ? undefined : initiativeIdMapper(parentId);
  return {
    _id: initiativeId,
    parentId: newParentId,
    idString: `${initiativeId}`,
    parentIdString: `${newParentId}`
  };
};

const createUtrv = (initiativeNum: number, value: number | undefined, utrId = universalTrackerId) => {
  const id = new ObjectId();
  const initiativeId = initiativeIdMapper(initiativeNum);
  return {
    _id: id,
    idString: `${id}`,
    initiativeId: initiativeId,
    initiativeIdString: `${initiativeId}`,
    universalTrackerId: utrId,
    universalTrackerIdString: `${utrId}`,
    effectiveDate: new Date(),
    type: 'baseline',
    value: value
  };
};

export const initiatives = [
  createInitiative(1),
  createInitiative(11, 1),
  createInitiative(111, 11),
  createInitiative(112, 11),
  createInitiative(113, 11),

  createInitiative(12, 1),
  createInitiative(121, 12),
  createInitiative(122, 12),
  createInitiative(123, 12),

  createInitiative(13, 1),
  createInitiative(131, 13),
  createInitiative(132, 13),
  createInitiative(133, 13),
];

export const universalTrackerValues = [
  {
    expectedResult: 50,
    description: 'Two children with values',
    universalTrackerId: mockPercentageUtr.idString,
    utrvs: [
      createUtrv(11, 40),
      createUtrv(12, 60),
    ]
  },
  {
    expectedResult: 100,
    description: 'Parent with value takes priority over children',
    universalTrackerId: mockPercentageUtr.idString,
    utrvs: [
      createUtrv(1, 100),
      createUtrv(11, 40),
      createUtrv(12, 60),
      createUtrv(13, 80),
    ]
  },
  {
    expectedResult: 100,
    description: 'Parent with value takes priority over grandchildren',
    universalTrackerId: mockPercentageUtr.idString,
    utrvs: [
      createUtrv(1, 100),
      createUtrv(111, 40),
      createUtrv(112, 40),
      createUtrv(113, 40),
      createUtrv(121, 60),
      createUtrv(122, 60),
      createUtrv(123, 60),
      createUtrv(131, 80),
      createUtrv(132, 80),
      createUtrv(133, 80),
    ]
  },
  {
    expectedResult: 60,
    description: 'Simple aggregation of all children',
    universalTrackerId: mockPercentageUtr.idString,
    utrvs: [
      createUtrv(11, 40),
      createUtrv(12, 60),
      createUtrv(13, 80),
    ],
  },
  {
    expectedResult: 10,
    description: 'Simple aggregation of single child',
    universalTrackerId: mockPercentageUtr.idString,
    utrvs: [
      createUtrv(11, 10),
    ],
  },
  {
    description: 'What happens if there are no UTRVs?',
    universalTrackerId: mockPercentageUtr.idString,
    expectedResult: undefined,
    utrvs: []
  },
  {
    expectedResult: 60,
    description: 'Aggregate one grandchild for each child',
    universalTrackerId: mockPercentageUtr.idString,
    utrvs: [
      createUtrv(111, 40),
      createUtrv(121, 60),
      createUtrv(131, 80),
    ],
  },
  {
    expectedResult: 60,
    description: 'Aggregate all grandchildren',
    universalTrackerId: mockPercentageUtr.idString,
    utrvs: [
      createUtrv(111, 40),
      createUtrv(112, 40),
      createUtrv(113, 40),
      createUtrv(121, 60),
      createUtrv(122, 60),
      createUtrv(123, 60),
      createUtrv(131, 80),
      createUtrv(132, 80),
      createUtrv(133, 80),
    ],
  },
  {
    expectedResult: 60,
    description: 'Aggregate all grandchildren with sub-aggregation',
    universalTrackerId: mockPercentageUtr.idString,
    utrvs: [
      createUtrv(111, 40),
      createUtrv(112, 60),
      createUtrv(113, 80),
      createUtrv(121, 40),
      createUtrv(122, 60),
      createUtrv(123, 80),
      createUtrv(131, 40),
      createUtrv(132, 60),
      createUtrv(133, 80),
    ],
  },
  {
    expectedResult: 40,
    description: 'Aggregate one child with value and one which is NA/NR',
    universalTrackerId: mockPercentageUtr.idString,
    utrvs: [
      createUtrv(11, 40),
      createUtrv(12, undefined),
    ],
  },
  {
    expectedResult: 'undefined',
    description: 'Aggregate where all children are NA/NR',
    universalTrackerId: mockPercentageUtr.idString,
    utrvs: [
      createUtrv(11, undefined),
      createUtrv(12, undefined),
    ],
  },
  {
    expectedResult: 180,
    description: 'Aggregation of all children with valueAggregation ValueAggregation.ValueSumAggregator override',
    universalTrackerId: mockSumAggregatedUtr.idString,
    utrvs: [
      createUtrv(11, 50, mockSumAggregatedUtr._id),
      createUtrv(12, 60, mockSumAggregatedUtr._id),
      createUtrv(13, 70, mockSumAggregatedUtr._id),
    ],
  },
  {
    expectedResult: 180,
    description: 'Aggregation of all children with valueAggregation ValueAggregation.ValueSumAggregator override',
    universalTrackerId: mockSumAggregatedUtr.idString,
    utrvs: [
      createUtrv(11, 50, mockSumAggregatedUtr._id),
      createUtrv(12, 60, mockSumAggregatedUtr._id),
      createUtrv(13, 70, mockSumAggregatedUtr._id),
    ],
  },
  {
    expectedResult: 180,
    description: 'Aggregation of all children with valueAggregation ValueAggregation.ValueAverageAggregation should ignore if aggregating children',
    universalTrackerId: mockAverageAggregatedUtr.idString,
    aggregationMode: AggregationMode.Children,
    utrvs: [
      createUtrv(11, 50, mockAverageAggregatedUtr._id),
      createUtrv(12, 60, mockAverageAggregatedUtr._id),
      createUtrv(13, 70, mockAverageAggregatedUtr._id),
    ],
  },
  {
    expectedResult: 60,
    description: 'Aggregation of all children with valueAggregation ValueAggregation.ValueAverageAggregation override',
    universalTrackerId: mockAverageAggregatedUtr.idString,
    aggregationMode: AggregationMode.Combined,
    utrvs: [
      createUtrv(11, 50, mockAverageAggregatedUtr._id),
      createUtrv(12, 60, mockAverageAggregatedUtr._id),
      createUtrv(13, 70, mockAverageAggregatedUtr._id),
    ],
  },
  {
    expectedResult: 70,
    description: 'Aggregation of all children with valueAggregation ValueAggregation.LatestAggregator override',
    universalTrackerId: mockLatestAggregatedUtr.idString,
    aggregationMode: AggregationMode.Combined,
    utrvs: [
      createUtrv(11, 50, mockLatestAggregatedUtr._id),
      createUtrv(12, 60, mockLatestAggregatedUtr._id),
      createUtrv(13, 70, mockLatestAggregatedUtr._id),
    ],
  },
]
