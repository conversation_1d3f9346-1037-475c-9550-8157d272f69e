/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { useParams } from 'react-router';
import Dashboard from '@g17eco/molecules/dashboard';
import { ReportDocumentCreate } from '@apps/company-tracker/routes/report-editor/components/ReportDocumentCreate';
import { useHistory } from 'react-router-dom';
import { generateUrl } from '@routes/util';
import { ROUTES } from '@constants/routes';
import { useGetReportDocumentQuery } from '@api/initiative-report-documents';
import { skipToken } from '@reduxjs/toolkit/query';
import { BlockingLoader } from '@g17eco/atoms/loader';

export const ReportEditorCreateRoute = () => {
  const { initiativeId, type, reportId } = useParams<{ initiativeId: string; type: string; reportId?: string }>();
  const history = useHistory();

  const { data: reportDocument, isFetching } = useGetReportDocumentQuery(
    reportId
      ? {
          initiativeId,
          reportId,
        }
      : skipToken,
  );

  const onCancel = () => {
    history.push(generateUrl(ROUTES.REPORT_EDITOR_LIST, { initiativeId, type }));
  };

  const onView = (reportId: string) => {
    history.push(generateUrl(ROUTES.REPORT_EDITOR_VIEW, { initiativeId, type, reportId }));
  };

  return (
    <Dashboard>
      {isFetching ? <BlockingLoader /> : null}
      <ReportDocumentCreate
        key={reportDocument?._id}
        onCancel={onCancel}
        onView={onView}
        initiativeId={initiativeId}
        type={type}
        reportDocument={reportDocument}
      />
    </Dashboard>
  );
};
