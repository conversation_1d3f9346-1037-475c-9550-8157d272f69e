/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { UniversalTrackerValuePlain } from '../../models/universalTrackerValue';
import { AggregatedUniversalTracker } from './aggregation/AggregatedUniversalTracker';
import AggregatorDataService, { AggregationUniversalTrackerPlain } from './aggregation/aggregatorDataService';
import { AggregationMode } from '../../models/public/universalTrackerType';
import { UtrValueFields } from './aggregation/utrTypeAggregator';
import { UtrvType } from './constants';

export type UTRV_TYPES = 'actual' | 'baseline' | 'target';
export type UTRV_MODELS = UtrValueFields | UniversalTrackerValuePlain;

export default class AggregatorService {

  constructor(
    private readonly universalTrackerId: string,
    private readonly types: UTRV_TYPES[],
    private dataService: AggregatorDataService = new AggregatorDataService(),
    private readonly aggregationMode = AggregationMode.Children,
    ) {
  }

  public async getAggregatedByInitiativeId(initiativeId: string, includeLedgerValues = false, isCompletedData = false, assuredOnly?: boolean) {
    const utrv = await this.dataService.getUtrvsByInitiativeId({
      utrId: this.universalTrackerId,
      initiativeId,
      types: this.types as UtrvType[],
      includeLedgerValues,
      isCompletedData,
      assuredOnly
    });

    if (utrv) {
      return utrv;
    }

    return this.getAggregatedValueByParentId(initiativeId);
  }

  private async getAggregatedValueByParentId(parentInitiativeId: string) {
    const childInitiatives = this.dataService.getInitiativesByParentId(parentInitiativeId);
    const initiativeIds: string[] = childInitiatives.map(i => i.idString);

    const utrvs: (UniversalTrackerValuePlain | AggregatedUniversalTracker | undefined)[] = await Promise.all(
      initiativeIds.map((id: string) => this.getAggregatedByInitiativeId(id))
    );

    const filteredUtrvs = utrvs.filter(utrv => utrv !== undefined);
    if (!filteredUtrvs || filteredUtrvs.length === 0) {
      return;
    }
    return await this.aggregate(filteredUtrvs);
  }

  private aggregate = async (utrvs: (UniversalTrackerValuePlain | AggregatedUniversalTracker)[]) => {
    const utr = await this.dataService.getUtrById(this.universalTrackerId);
    const aggUtrv = new AggregatedUniversalTracker(utr as AggregationUniversalTrackerPlain, [], UtrvType.Actual, this.aggregationMode);
    utrvs.forEach(utrv => aggUtrv.add(utrv));
    return aggUtrv;
  }

}
