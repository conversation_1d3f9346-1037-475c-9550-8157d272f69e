// ***************************************************************************
// size helpers
// ***************************************************************************

$sizes: (
  xs: 0.25,
  sm: 0.5,
  md: 1,
  lg: 2,
  xl: 4,
);

@each $k1, $v1 in $sizes {
  @each $k3, $v3 in $container-max-widths {
    .w-#{$k1} {
      width: $v3 * $v1;
    }
  }
}

// ***************************************************************************
// colour helpers
// ***************************************************************************

@each $k1, $v1 in $styleGuideTheme {
  .text-Theme#{$k1} {
    color: $v1 !important;
  }

  .background-Theme#{$k1} {
    background-color: $v1 !important;
  }

  .border-Theme#{$k1} {
    border-color: $v1 !important;
  }
}

@each $k1, $v1 in $styleGuideColour {
  .text-#{$k1} {
    color: $v1 !important;
  }

  .background-#{$k1} {
    background-color: $v1 !important;
  }

  .border-#{$k1} {
    border-color: $v1 !important;
  }
}

// Special Gradient colours
.text-gradient {
  background: $gradientGradientsCcGradient;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
}

.background-gradient {
  background: $gradientGradientsCcGradient;
}

.border-radius {
  border-radius: $border-radius;
}

.border-gradient {
  border: unset !important;
  border-width: 1px !important;
  border-style: solid !important;
  border-image: $gradientGradientsCcGradient 1 !important;
}

// CSS native styles

:root {
  @each $k1, $v1 in $styleGuideTheme {
    --theme-#{$k1}: #{$v1};
  }
  @each $k1, $v1 in $styleGuideColour {
    --theme-#{$k1}: #{$v1};
  }
  --theme-font-family: "InterVariable", sans-serif;
  --gradients-cc-gradient: #{$gradientGradientsCcGradient};
  --gradients-cc-gradient-20: #{$gradientGradientsCcGradient20};
  --fa-width: auto; // Backwards compatibility with fontawesome v6
}

.hidden {
  display: none;
}
.cursor-pointer {
  cursor: pointer;
}

// ***************************************************************************
// spacing helpers
// ***************************************************************************

@each $key, $space in $spacing {
  .m-#{$key} {
    margin: $space !important;
  }
  .ms-#{$key} {
    margin-left: $space !important;
  }
  .-ms-#{$key} {
    margin-left: -$space !important;
  }
  .me-#{$key} {
    margin-right: $space !important;
  }
  .mt-#{$key} {
    margin-top: $space !important;
  }
  .-mt-#{$key} {
    margin-top: -$space !important;
  }
  .mb-#{$key} {
    margin-bottom: $space !important;
  }

  .p-#{$key} {
    padding: $space !important;
  }
  .ps-#{$key} {
    padding-left: $space !important;
  }
  .pe-#{$key} {
    padding-right: $space !important;
  }
  .pt-#{$key} {
    padding-top: $space !important;
  }
  .pb-#{$key} {
    padding-bottom: $space !important;
  }
  .px-#{$key} {
    padding-left: $space !important;
    padding-right: $space !important;
  }
  .py-#{$key} {
    padding-top: $space !important;
    padding-bottom: $space !important;
  }
}

// ***************************************************************************
// text helpers
// ***************************************************************************

@each $key, $size in $paragraphs {
  .text-#{$key} {
    font-size: map-get($size, 'font-size') !important;
    line-height: map-get($size, 'line-height') !important;
  }
}

.text-strong, b, strong, .strong {
  font-weight: 700 !important;
}

.text-medium {
  font-weight: 500 !important;
}

.text-label {
  font-size: 0.6875rem !important; // 11px
  line-height: 100% !important;
  letter-spacing: 0.034375rem !important; // 5%
}

.text-label-uppercase {
  @extend .text-label;
  text-transform: uppercase;
}

// Z-INDEX helpers
@each $step in 0, 1, 2, 3, 4, 5, auto {
  .z-#{$step} {
    z-index: $step;
  }
}

.shadow-swatch {
  box-shadow: $shadowSwatch;
}

.shadow-sm {
  box-shadow: $shadowThemeShadowSmall;
}

.shadow-md {
  box-shadow: $shadowThemeShadowMedium;
}

.shadow-lg {
  box-shadow: $shadowThemeShadowLarge;
}

.shadow-xl {
  box-shadow: $shadowThemeShadowXl;
}

.shadow-swatch-alt {
  box-shadow: $shadowSwatch2;
}

.shadow-sm-alt {
  box-shadow: $shadowThemeShadowSmall2;
}

.shadow-md-alt {
  box-shadow: $shadowThemeShadowMedium2;
}

.shadow-lg-alt {
  box-shadow: $shadowThemeShadowLarge2;
}

.shadow-xl-alt {
  box-shadow: $shadowThemeShadowXl2;
}
