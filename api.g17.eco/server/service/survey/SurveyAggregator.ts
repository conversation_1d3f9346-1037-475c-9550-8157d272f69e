/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import type { SurveyUtrvs} from '../../repository/UniversalTrackerRepository';
import { UniversalTrackerRepository } from '../../repository/UniversalTrackerRepository';
import { AggregatedUniversalTracker } from '../utr/aggregation/AggregatedUniversalTracker';
import { ActionList, DataPeriods, UtrvType } from '../utr/constants';
import type { SourceItem, UniversalTrackerValueAggregated } from '../../models/universalTrackerValue';
import type {
  AggregatedSurveyFilters,
  SurveyModel,
  SurveyModelPlain} from '../../models/survey';
import Survey, {
  defaultSurveyFilters,
  SourceItemType,
  SurveyType,
} from '../../models/survey';
import type { SurveyMinDataCreate } from './SurveyClone';
import { SurveyClone } from './SurveyClone';
import { SurveyRepository } from '../../repository/SurveyRepository';
import type { InitiativePlain } from '../../models/initiative';
import Initiative from '../../models/initiative';
import { extractVisibleUtrCodes } from '../../survey/surveyForms';
import type { Blueprint, BlueprintRepositoryInterface} from '../../repository/BlueprintRepository';
import { getBlueprintRepository } from '../../repository/BlueprintRepository';
import type { UserPlain } from '../../models/user';
import User from '../../models/user';
import { SourceTypes } from '../../models/public/universalTrackerValueType';
import type { KeysEnum } from '../../models/public/projectionUtils';
import { SurveyScope } from './SurveyScope';
import type { SurveyComposer } from './SurveyComposer';
import { createSurveyComposer } from './SurveyComposer';
import { excludeSoftDeleted } from '../../repository/aggregations';
import { AggregationMode } from '../../models/public/universalTrackerType';
import { SurveyAggregateProcess } from './SurveyAggregateProcess';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { arrayToTree, flattenTree } from '../../util/tree';
import type { FilterQuery } from 'mongoose';
import type { SurveyManager} from './SurveyManager';
import { getSurveyManager } from './SurveyManager';
import type { LoggerInterface} from '../wwgLogger';
import { wwgLogger } from '../wwgLogger';
import ContextError from '../../error/ContextError';
import { valueChainCategories } from '../../util/valueChain';
import { getBestValuesForIntiativeTree } from '../utr/aggregation/utils';
import type { AggregatedCloneParams } from './model/ProcessData';
import UserError from '../../error/UserError';
import { UtrvFilter } from '../../models/insightDashboard';
import { UtrvStatuses } from '../../types/universalTrackerValue';
import { blueprintDefaultUnitConfig } from '../units/unitTypes';
import { SURVEY_AGGREGATOR_VERSION } from './constants';

interface AggregateOptions {
  surveyIds: ObjectId[];
  utrIds: ObjectId[];
  types?: string[];
  initiativeId?: ObjectId;
  aggregationMode: AggregationMode;
  utrvStatuses: ActionList[];
}

interface BaseAggregateSetup extends Omit<SurveyMinDataCreate, 'effectiveDate' | 'sourceItems' | 'unitConfig'> {
  type: SurveyType.Aggregation | SurveyType.AutoAggregation;
  effectiveDate?: string;
  surveyIdsToAggregate: ObjectId[];
  aggregationMode?: AggregationMode;
  filters?: AggregatedSurveyFilters;
}

interface AggregateSetup extends Omit<BaseAggregateSetup, 'surveyIdsToAggregate'> {
  surveysToAggregate: AggregateSurveyCreate[];
  currency?: string;
  useAISummary?: boolean;
}

export type AggregateSurveyCreate = Pick<
  SurveyModel,
  '_id' | 'scope' | 'period' | 'initiativeId' | 'effectiveDate' | 'unitConfig'
>;

export class SurveyAggregator {
  static VERSION: number = SURVEY_AGGREGATOR_VERSION; // YYYYMMDD

  constructor(
    private blueprintRepo: BlueprintRepositoryInterface,
    private surveyComposer: SurveyComposer,
    private surveyManager: SurveyManager,
    private logger: LoggerInterface
  ) {}

  public convertToUtrvStatuses(status: UtrvFilter.AllAnswered | UtrvFilter.Verified) {
    if ([UtrvFilter.AllAnswered, UtrvFilter.Verified].includes(status)) {
      return UtrvStatuses[status];
    }

    this.logger.error(new UserError('Unsupported utrv status', { utrvStatus: status }));
    return [];
  }

  public async aggregateSurveyIds(options: AggregateOptions) {
    if (options.surveyIds.length === 0) {
      throw new ContextError('No surveys ids to aggregate', {
        surveyIds: options.surveyIds.length,
        utrIds: options.utrIds.length,
        types: options.types,
        initiativeId: options.initiativeId,
        aggregationMode: options.aggregationMode
      });
    }

    const data = await UniversalTrackerRepository.getSurveyUtrvs({
      surveyIds: options.surveyIds,
      utrIds: options.utrIds,
      statuses: options.utrvStatuses,
    });
    return this.aggregateUtrvs(data, options.aggregationMode);
  }

  private async aggregateUtrvs(data: Pick<SurveyUtrvs, 'universalTracker' | 'utrvs'>[], aggregationMode: AggregationMode) {
    return data.map(({ universalTracker, utrvs }) => {
      return new AggregatedUniversalTracker(universalTracker, utrvs, UtrvType.Actual, aggregationMode);
    });
  }

  public async getAutoAggregatedSurveyIds(initiativeId: ObjectId | string) {
    const initiative = await Initiative.findById(initiativeId).orFail().exec();
    const allInitiativeSubsidiaries = await InitiativeRepository.getMainTreeChildren(initiative._id);
    // only get surveys from children levels
    const subsidiaryIds = allInitiativeSubsidiaries.reduce((acc, cur) => {
      if (cur._id.toString() !== initiativeId.toString()) {
        acc.push(cur._id);
      }
      return acc;
    }, [] as ObjectId[]);

    return SurveyRepository.findInitiativeLatestSurveys(subsidiaryIds);
  }

  /** @todo: Refactor sourceItems and status logic into AggregatedUniversalTracker class */
  public convertToUtrvExtended(d: AggregatedUniversalTracker, initiativeId: ObjectId): UniversalTrackerValueAggregated {

    const currentSourceItems = d.sourceItems ?? [];
    const sourceItems = [
      ...currentSourceItems, // If this is aggregating AggregatedUtrvs, then we need to keep the source items
      ...d.disaggregation.reduce((acc, dv) => {
        const existed = currentSourceItems.some((s) => {
          // Check if the source item already exists (existing logic using updateHistory)
          return s.utrvId.equals(dv._id) && s.historyId.equals(dv.updatedHistory?._id);
        });

        if (existed) {
          return acc;
        }
        // Keep it here looking for updatedHistory, as it is required prop
        if (dv.updatedHistory?._id && dv._id) {
          acc.push({
            utrvId: new ObjectId(dv._id),
            historyId: dv.updatedHistory._id,
            latestHistoryId: dv.latestHistory?._id,
            latestStatus: dv.latestHistory?.action,
          } satisfies SourceItem);
        }
        return acc;
      }, [] as SourceItem[]),
    ];

    return {
      _id: d.utr._id,
      initiativeId,
      value: d.value,
      type: d.type ?? UtrvType.Actual,
      valueType: d.valueType,
      period: d.period,
      effectiveDate: d.effectiveDate,
      valueData: d.valueData,
      universalTracker: d.utr,
      universalTrackerId: d.utr._id,
      history: [],
      lastUpdated: new Date(),
      evidenceRequired: false,
      verificationRequired: false,
      status: ActionList.Verified,
      instructions: '',
      sourceType: SourceTypes.Aggregated,
      valueValidation: d.utr.valueValidation ?? {},
      sourceItems: sourceItems,
      valueAggregation: d.valueAggregation,
    };
  }

  public async createAggregatedSurvey(options: AggregateSetup): Promise<SurveyModel> {
    const { company: initiative, user, effectiveDate, surveysToAggregate, type, filters, currency } = options;

    wwgLogger.info(`Processing aggregated survey for initiative ${initiative.name}. Step 1/7 completed.`);
    const surveyIds = surveysToAggregate.map((survey) => survey._id);

    if (surveysToAggregate.length === 0) {
      throw new ContextError('No surveys ids to aggregate', {
        surveysToAggregate: options.surveysToAggregate.length,
        initiativeId: options.company._id,
        aggregationMode: options.aggregationMode
      });
    }

    const scope = options.scope ?? SurveyScope.mergeScopes(surveysToAggregate);
    // This will sort the original array.
    const surveyDate = this.getEffectiveDate(surveysToAggregate, effectiveDate);

    const surveyFilters = filters ?? defaultSurveyFilters;

    const surveyData = SurveyClone.createSurveyMinData({
      ...options,
      scope: scope,
      type,
      effectiveDate: surveyDate,
      sourceItems: surveyIds.map((id) => ({
        sourceId: id,
        type: SourceItemType.Survey,
      })),
      unitConfig: { ...blueprintDefaultUnitConfig, currency: currency ?? blueprintDefaultUnitConfig.currency },
    });

    wwgLogger.info(`Processing aggregated survey for initiative ${initiative.name}. Step 2/7 completed.`);

    const survey = new Survey({ ...surveyData, filters: surveyFilters });
    survey.aggregatedVersion = SurveyAggregator.VERSION,

    survey.blueprint = await this.surveyComposer.composeBlueprint(survey);
    const expandedBlueprint = await this.blueprintRepo.getExpandedBlueprint(survey.blueprint);

    const surveyProcess = await SurveyAggregateProcess.createSurveyAggregatedProcess(expandedBlueprint, user, survey);

    wwgLogger.info(`Processing aggregated survey ${survey.name} for initiative ${initiative.name}. Step 3/7 completed.`);

    // Get aggregated data
    const cloneData = await this.getAggregatedData({
      expandedBlueprint,
      surveysToAggregate,
      initiative,
      utrvStatuses: this.convertToUtrvStatuses(surveyFilters.utrv),
    });
    wwgLogger.info(`Processing aggregated survey ${survey.name} for initiative ${initiative.name}. Step 4/7 completed.`);

    await surveyProcess.process(cloneData);
    wwgLogger.info(
      `Processing aggregated survey ${survey.name} for initiative ${initiative.name}. Step 5/7 completed.`
    );

    const savedSurvey = await surveyProcess.saveUpdate();
    wwgLogger.info(
      `Processing aggregated survey ${survey.name} for initiative ${initiative.name}. Step 6/7 completed.`
    );

    await this.surveyManager.processAction(savedSurvey, user, 'recalculate'); // Fix up all the SDG calculations
    wwgLogger.info(
      `Processing aggregated survey ${survey.name} for initiative ${initiative.name}. Step 7/7 completed.`
    );

    return savedSurvey;
  }

  private getEffectiveDate(surveysToAggregate: AggregateSurveyCreate[], effectiveDate: string | undefined) {
    if (effectiveDate) {
      return new Date(effectiveDate);
    }

    const [last] = surveysToAggregate.sort((a, b) =>
      a.effectiveDate < b.effectiveDate ? 1 : a.effectiveDate > b.effectiveDate ? -1 : 0
    );
    return new Date(last.effectiveDate);
  }

  private getAggregatedSurveyFilters({
    survey,
    config,
  }: {
    survey: Pick<SurveyModel, 'filters'>;
    config: Pick<AggregateSetup, 'filters'>;
  }): AggregatedSurveyFilters {
    if (config.filters) {
      return config.filters;
    }
    if (survey.filters) {
      return survey.filters;
    }
    return defaultSurveyFilters;
  }

  // update combined report's configuration
  public async updateAggregatedSurveyConfig({
    data,
    initiative,
    user,
  }: {
    data: Pick<AggregateSetup, 'name' | 'period' | 'scope' | 'filters'> & {
      surveyId: ObjectId;
      aggregatedSurveyIds: ObjectId[];
    };
    initiative: InitiativePlain;
    user: UserPlain;
  }) {
    const { surveyId, aggregatedSurveyIds } = data;
    const surveysToAggregate = await this.getSurveysForAggregation(aggregatedSurveyIds);

    if (surveysToAggregate.length === 0) {
      throw new UserError('You must select at least one survey', {
        aggregatedSurveyIds
      });
    }

    const survey = await SurveyRepository.mustFindById(surveyId);
    survey.sourceItems = surveysToAggregate.map((survey) => ({
      sourceId: survey._id,
      type: SourceItemType.Survey,
    }));
    survey.scope = data.scope;
    survey.name = data.name;
    survey.period = data.period ?? DataPeriods.Yearly;
    survey.blueprint = await this.surveyComposer.composeBlueprint(survey);
    survey.filters = this.getAggregatedSurveyFilters({ survey, config: data });

    await survey.save();

    return this.updateAggregatedSurvey(survey, initiative, user);
  }

  public async cloneAggregatedSurvey({ newSurvey, user, initiative }: AggregatedCloneParams) {
    if (!newSurvey.blueprint) {
      newSurvey.blueprint = await this.surveyComposer.composeBlueprint(newSurvey);
    }
    return this.updateAggregatedSurvey(newSurvey, initiative, user);
  }

  // refresh aggregated data
  public async updateAggregatedSurvey(
    survey: SurveyModel,
    initiative: Pick<InitiativePlain, '_id'>,
    user: UserPlain,
  ) {
    const expandedBlueprint = survey.blueprint
      ? await this.blueprintRepo.getExpandedBlueprint(survey.blueprint)
      : await this.blueprintRepo.mustFindExpandedByCode(survey.sourceName);

    const surveyProcess = await SurveyAggregateProcess.createSurveyAggregatedProcess(expandedBlueprint, user, survey);

    if (!survey.sourceItems) {
      survey.sourceItems = [];
    }

    const surveyIds = survey.sourceItems.reduce<ObjectId[]>((a, c) => {
      if (c.type === SourceItemType.Survey) {
        a.push(c.sourceId);
      }
      return a;
    }, []);

    const surveys = await Survey.find({
      _id: { $in: surveyIds },
      deletedDate: { $exists: false },
    }, { _id: 1, initiativeId: 1 })
    .lean<Pick<SurveyModelPlain, '_id' | 'initiativeId'>[]>();

    // Existing aggregated surveys don't have utrv filters
    const utrvStatuses = this.convertToUtrvStatuses(survey.filters?.utrv ?? UtrvFilter.Verified);
    // Get aggregated data
    const cloneData = await this.getAggregatedData({
      expandedBlueprint,
      surveysToAggregate: surveys,
      initiative,
      utrvStatuses,
    });
    await surveyProcess.process(cloneData);

    survey.aggregatedDate = new Date();
    survey.aggregatedVersion = SurveyAggregator.VERSION;
    const savedSurvey = await surveyProcess.saveUpdate();

    await this.surveyManager.processAction(savedSurvey, user, 'recalculate'); // Fix up all the SDG calculations

    return savedSurvey;
  }

  public async preUpdateAggregatedSurvey(survey: SurveyModel, initiativeId: string | ObjectId) {
    const surveyIds = await this.getAutoAggregatedSurveyIds(initiativeId);

    const surveysToAggregate = await this.getSurveysForAggregation(surveyIds);

    survey.sourceItems = surveysToAggregate.map((survey) => ({
      sourceId: survey._id,
      type: SourceItemType.Survey,
    }));

    survey.scope = SurveyScope.mergeScopes(surveysToAggregate);
    survey.effectiveDate = this.getEffectiveDate(surveysToAggregate, new Date().toISOString());
    survey.blueprint = await this.surveyComposer.composeBlueprint(survey);
    await survey.save();
    return survey;
  }

  public async getAggregatedData(
    {
      expandedBlueprint,
      surveysToAggregate,
      initiative,
      utrvStatuses,
    }: {
      expandedBlueprint: Pick<Blueprint, 'forms'>;
      surveysToAggregate: Pick<SurveyModelPlain, '_id' | 'initiativeId'>[];
      initiative: Pick<InitiativePlain, '_id'>;
      utrvStatuses: ActionList[];
    }
  ): Promise<UniversalTrackerValueAggregated[]> {

    // There are multiple ways to aggregate data:
    //  - AggregationMode.Combined: Aggregate all the data for surveys on the same level (siblings):
    //  - AggregationMode.Children: Aggregate all the data from the children

    // OBJECTIVE
    //  1. Group surveys by initiativeId
    //  2. For each Initiative level, use AggregationMode.Combined (as they are siblings). This results in one survey per initiative
    //  3. Then we need to traverse the initiative tree. For each UTRV, we need to ignore any values that are descendents (further down the tree)
    //  4. With what is left, we can now use AggregationMode.Children.

    // Preparations
    const codes = extractVisibleUtrCodes(expandedBlueprint);
    const utrs = await UniversalTrackerRepository.find({ code: { $in: codes } }, { _id: 1 });
    const utrIds = utrs.map((u) => u._id)

    // 1. Group surveys by initiativeId
    const surveysByInitiative = surveysToAggregate.reduce((acc, survey) => {
      const id = survey.initiativeId.toString();
      if (!acc[id]) {
        acc[id] = [];
      }
      acc[id].push(survey);
      return acc;
    }, {} as Record<string, Pick<SurveyModelPlain, '_id' | 'initiativeId'>[]>);

    // 2. For each Initiative, use AggregationMode.Combined
    const entries = Object.entries(surveysByInitiative);
    const batchSize = 10; // Adjust the batch size as needed
    const batches: (typeof entries)[] = [];
    for (let i = 0; i < entries.length; i += batchSize) {
      batches.push(entries.slice(i, i + batchSize));
    }

    const combinedDataArray: UniversalTrackerValueAggregated[][] = [];
    for (const batch of batches) {
      const batchPromises = batch.map(async ([initiativeIdStr, surveys]) => {
        const initiativeId = new ObjectId(initiativeIdStr);
        const surveyIds = surveys.map((s) => s._id);
        const data = await UniversalTrackerRepository.getSurveyUtrvs({ surveyIds, utrIds, statuses: utrvStatuses });
        const utrvs = await this.aggregateUtrvs(data, AggregationMode.Combined);
        return utrvs.map((d) => ({
          ...this.convertToUtrvExtended(d, initiativeId),
          isCloned: true,
          _id: undefined,
        }));
      });

      const batchResults = await Promise.all(batchPromises);
      combinedDataArray.push(...batchResults);
    }

    //  3. Then we need to traverse the initiative tree. For each UTRV, we need to ignore any values that are descendents (further down the tree)
    const rootInitiativeId = initiative._id;
    const initiatives = await InitiativeRepository.getAllChildrenById<Pick<InitiativePlain, '_id' | 'parentId'>>(
      rootInitiativeId,
      { tags: { $nin: valueChainCategories }
    }, { _id: 1, parentId: 1 });

    const initiativesTree = arrayToTree(initiatives, rootInitiativeId);

    const rootInitiative = initiativesTree[0];
    const utrvs = getBestValuesForIntiativeTree(rootInitiative, combinedDataArray.flat());

    const combinedUtrvs = utrvs.reduce((acc, cur) => {
      const utrCode = cur.universalTracker.code;
      const existing = acc.get(utrCode);
      if (existing) {
        existing.utrvs.push(cur);
      } else {
        acc.set(utrCode, {
          universalTracker: cur.universalTracker,
          utrvs: [cur]
        });
      }
      return acc;
    }, new Map<string, Pick<SurveyUtrvs, 'universalTracker' | 'utrvs'>>());

    //  4. With what is left, we can now use AggregationMode.Children.
    const aggregatedUtrvs = await this.aggregateUtrvs(Array.from(combinedUtrvs.values()), AggregationMode.Children);
    return aggregatedUtrvs.map((d) => ({
      ...this.convertToUtrvExtended(d, initiative._id),
      isCloned: true,
      _id: undefined,
    }));
  }

  public async getSurveysForAggregation(surveyIds: ObjectId[]): Promise<AggregateSurveyCreate[]> {
    const project: KeysEnum<AggregateSurveyCreate> = {
      _id: 1,
      initiativeId: 1,
      effectiveDate: 1,
      scope: 1,
      period: 1,
      unitConfig: 1,
    };

    return SurveyRepository.findSurveys(
      {
        _id: { $in: surveyIds },
        ...excludeSoftDeleted(),
      },
      project,
      true
    );
  }

  public async getInitiativeSurveysForAggregation({
    initiativeIds,
    startDate,
    endDate,
    period,
  }: {
    initiativeIds: ObjectId[];
    startDate: Date;
    endDate: Date;
    period?: DataPeriods;
  }): Promise<AggregateSurveyCreate[]> {
    const project: KeysEnum<AggregateSurveyCreate> = {
      _id: 1,
      initiativeId: 1,
      effectiveDate: 1,
      scope: 1,
      period: 1,
      unitConfig: 1,
    };

    const $match: FilterQuery<SurveyModel> = {
      type: SurveyType.Default,
      initiativeId: { $in: initiativeIds },
      effectiveDate: {
        $gte: startDate,
        $lte: endDate,
      },
      ...excludeSoftDeleted(),
    };

    if (period === DataPeriods.Quarterly) {
      $match.period = {
        $in: [DataPeriods.Quarterly, DataPeriods.Monthly],
      };
    }
    if (period === DataPeriods.Monthly) {
      $match.period = {
        $in: [DataPeriods.Monthly],
      };
    }

    return SurveyRepository.findSurveys($match, project, true);
  }

  public async updateAll({ userId, initiativeId }: { userId: ObjectId; initiativeId: ObjectId }) {
    const user = await User.findById(userId).orFail().exec();
    const initiative = await Initiative.findById(initiativeId).orFail().exec();

    // Initiative and its children.
    const unorderedInitiatives = await InitiativeRepository.getMainTreeChildren(initiative._id);

    // Sort initiatives by level from leafs to root.
    const initiativesTree = arrayToTree(unorderedInitiatives, initiative._id);
    const initiativesByLevel = flattenTree(initiativesTree).reverse() as InitiativePlain[];

    // Search all first to reduce db call
    const aggregatedSurveys = await Survey.find({
      initiativeId: { $in: initiativesByLevel.map((item) => item._id) },
      type: SurveyType.Aggregation,
      deletedDate: { $exists: false },
    });

    // Then update by initiative.
    const results = [];
    for (const initiative of initiativesByLevel) {
      const surveys = aggregatedSurveys.filter(({ initiativeId }) => initiativeId.equals(initiative._id));

      if (!surveys.length) {
        continue;
      }

      const result = await Promise.allSettled(
        surveys.map((survey) => this.updateAggregatedSurvey(survey, initiative, user))
      );

      const successIds = result.reduce((prev, cur) => {
        if (cur.status === 'fulfilled') {
          prev.push(cur.value._id );
        }

        return prev;
      }, [] as ObjectId[]);
      const failedIds = surveys
        .map(({ _id }) => _id)
        .filter((id) => !successIds.some((successId) => successId.equals(id)));
      results.push({
        successIds,
        failedIds,
      });
    }

    return results;
  }

  /** @todo: Refactor aggregated survey templates and auto aggregated surveys handle currency */
  private hasSingleCurrency(surveysToAggregate: AggregateSurveyCreate[]) {
    const currencies = new Set(surveysToAggregate.map((survey) => survey.unitConfig.currency));
    return currencies.size === 1;
  }

  /** Create aggregated survey with validation like unitConfig */
  public async createAggregatedSurveyWithValidation(options: BaseAggregateSetup) {
    const { surveyIdsToAggregate, ...rest } = options;
    const surveysToAggregate = await this.getSurveysForAggregation(surveyIdsToAggregate);
    if (surveysToAggregate.length === 0) {
      throw new UserError('Cannot aggregate surveys. No surveys found.', {
        surveyIds: surveyIdsToAggregate.map((id) => id.toString()),
      });
    }

    const isValidCurrencyConfigs = this.hasSingleCurrency(surveysToAggregate);

    if (!isValidCurrencyConfigs) {
      throw new UserError('Cannot aggregate surveys with different currencies', {
        surveyIds: surveysToAggregate.map((survey) => survey._id.toString()),
        currencies: surveysToAggregate.map((survey) => survey.unitConfig.currency),
      });
    }

    return this.createAggregatedSurvey({
      surveysToAggregate,
      currency: surveysToAggregate[0].unitConfig.currency,
      ...rest,
    });
  }
}

let instance: SurveyAggregator;
export const getSurveyAggregator = () => {
  if (!instance) {
    instance = new SurveyAggregator(getBlueprintRepository(), createSurveyComposer(), getSurveyManager(), wwgLogger);
  }
  return instance;
};
