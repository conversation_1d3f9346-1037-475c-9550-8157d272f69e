/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import {
  Document,
  Header,
  Table,
  TableRow,
  PageOrientation,
  WidthType,
  ImageRun,
  TableLayoutType,
} from 'docx';
import { SurveyInitiative, UnitConfig } from '@g17eco/types/survey';
import {
  paragraphWithBoldText,
  paragraphArray,
  heading,
  spacer,
  Appendix,
  Styles,
  ArrayToTableCellType,
  cellToTableCell,
  imageWrapper,
  getWWGLogoLandscape,
  DEFAULTS,
  convertImgToBase64URL
} from './document-structure';
import { formatDate } from '../../utils/date';
import { ReportData } from '../../types/reportData';
import { renderQuestionValue, renderTable } from './questionView';
import { declarativeStyles } from './styles';
import { naturalSort } from '../../utils';
import { UtrValueTypes } from '../../utils/universalTracker';
import { getFrameworkTags, getLatestNote } from './group-builder';
import { DownloadMultiScope } from '../../types/download';
import { ReportGeneratorFn } from './types';
import { QUESTION } from '@constants/terminology';
import { standards } from '@g17eco/core';

const COLUMN_COUNT = 9;
const CODE = 'sam_csa';

export const table = (data: TableRow[], size = 8640) => {
  const columnWidths = [
    size / COLUMN_COUNT,
    size / COLUMN_COUNT,
    size / COLUMN_COUNT,
    size / COLUMN_COUNT,
    size / COLUMN_COUNT,
    size / COLUMN_COUNT,
    size / COLUMN_COUNT,
    size / COLUMN_COUNT,
    size / COLUMN_COUNT,
    size / COLUMN_COUNT,
  ];
  return new Table({
    rows: data,
    columnWidths: columnWidths,
    width: {
      size,
      type: WidthType.DXA,
    },
    layout: TableLayoutType.AUTOFIT,
  })
}

const createCell = (cell: ArrayToTableCellType, styles?: Styles) => {
  return cellToTableCell(cell, { ...styles, borders: DEFAULTS.TABLE_NO_BORDERS });
}

const getValueStr = (m: ReportData, unitConfig?: UnitConfig, displayUserInput?: boolean) => {
  if (m.universalTracker.valueType === UtrValueTypes.table) {
    const table = renderTable({ question: m, returnEmpty: false, unitConfig, displayUserInput });
    if (!table) {
      return;
    }
    return Appendix.add(m.universalTracker.name, table);
  }

  return renderQuestionValue(m, unitConfig, undefined, { displayUserInput });
}

export const getNote = (currentData: ReportData) => {
  const comment = currentData.note ?? '';
  if (comment.length <= 100) {
    return comment;
  }

  const lines = comment.trim().split('\n')
  const c = paragraphArray(lines)
  return Appendix.add(currentData.universalTracker.name, c)
};

const createRow = (currentData: ReportData, unitConfig?: UnitConfig, displayUserInput?: boolean) => {
  const companyResponse = getValueStr(currentData, unitConfig, displayUserInput);
  const code = getTypeCode(currentData);

  return new TableRow({
    children: [
      createCell(code),
      createCell(getName(currentData)),
      createCell(companyResponse ?? ''),
      createCell(getNote(currentData)),
      createCell(getFrameworkTags('tcfd', currentData.universalTracker)),
      createCell(getFrameworkTags('ungc', currentData.universalTracker)),
      createCell(getFrameworkTags('cdsb', currentData.universalTracker)),
      createCell(getFrameworkTags('sdg', currentData.universalTracker)),
      createCell(getLatestNote(currentData))
    ]
  });
}

const getName = (a: ReportData) => {
  if (a.universalTracker.alternatives?.[CODE]) {
    return a.universalTracker.alternatives?.[CODE]?.valueLabel ?? '';
  }
  return a.universalTracker.valueLabel;
}


const getTypeCode = (a: ReportData) => {
  if (a.universalTracker.alternatives?.[CODE]) {
    return a.universalTracker.alternatives?.[CODE]?.typeCode ?? '';
  }
  if (a.universalTracker.type === CODE) {
    return a.universalTracker.typeCode;
  }
  return '';
}

const getGroupData = (data: ReportData[], unitConfig?: UnitConfig, displayUserInput?: boolean) => {
  const rows: TableRow[] = data
    .sort((a, b) => naturalSort(getTypeCode(a), getTypeCode(b)))
    .map((currentData) => createRow(currentData, unitConfig, displayUserInput))

  return rows;
}

export const DJSIReportGenerator: ReportGeneratorFn<Document> = async (
  survey: SurveyInitiative,
  reportData,
  downloadScope: DownloadMultiScope
): Promise<Document> => {
  const samLogoBase64 = await convertImgToBase64URL(standards.sam_csa.src);

  const wwgLogoLandscape = await getWWGLogoLandscape();

  const logo = new ImageRun({
    type: 'png',
    data: Uint8Array.from(atob(samLogoBase64), c => c.charCodeAt(0)),
    transformation: {
      width: 100,
      height: 100
    }
  });

  Appendix.init();

  const { displayUserInput } = downloadScope;
  const tableRows = getGroupData(reportData, survey.unitConfig, displayUserInput);
  const tableHeaderRow = new TableRow({
    children: [
      createCell(`${QUESTION.CAPITALIZED_SINGULAR} No.`, { style: 'bold' }),
      createCell('Disclosure Name', { style: 'bold' }),
      createCell('Company Response', { style: 'bold' }),
      createCell('Comments', { style: 'bold' }),
      createCell('TCFD', { style: 'bold' }),
      createCell('UNGC10 Principles', { style: 'bold' }),
      createCell('CDSB', { style: 'bold' }),
      createCell('SDG Target', { style: 'bold' }),
      createCell('Note / Explain', { style: 'bold' })
    ]
  });

  const periodCovered = formatDate(survey.effectiveDate, 'MMMM YYYY')


  const document = new Document({
    styles: {
      paragraphStyles: declarativeStyles
    },
    sections: [
      {
        properties: {
          page: {
            size: {
              orientation: PageOrientation.LANDSCAPE,
            }
          }
        },
        headers: {
          default: new Header({
            children: [
              imageWrapper(logo),
              imageWrapper(wwgLogoLandscape)
            ]
          })
        },
        children: [
          heading(`DJSI Corporate Sustainability Assessment (CSA) ${QUESTION.CAPITALIZED_PLURAL}`),

          paragraphWithBoldText('Company Name: ', survey.initiative.name),
          paragraphWithBoldText('Company Sector: ', survey.initiative.industryText ?? ''),
          paragraphWithBoldText('Report Date: ', periodCovered),
          spacer(),

          table([tableHeaderRow, ...tableRows], 14000),

          ...Appendix.render()
        ]
      }
    ]
  })

  return document;
}


