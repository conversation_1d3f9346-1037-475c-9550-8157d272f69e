/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { loadSVGFromUrl } from './image';

// Mock fetch for testing
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('SVG loading utilities for docx', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('loadSVGFromUrl', () => {
    it('should load SVG as Uint8Array successfully', async () => {
      const mockSVGContent = '<svg><rect width="100" height="100"/></svg>';
      const mockArrayBuffer = new TextEncoder().encode(mockSVGContent).buffer;

      mockFetch.mockResolvedValueOnce({
        ok: true,
        arrayBuffer: () => Promise.resolve(mockArrayBuffer),
      });

      const result = await loadSVGFromUrl('test.svg');

      expect(result).toBeInstanceOf(Uint8Array);
      expect(new TextDecoder().decode(result)).toBe(mockSVGContent);
      expect(mockFetch).toHaveBeenCalledWith('test.svg', {
        mode: 'no-cors',
        headers: [],
      });
    });

    it('should throw error when fetch fails', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
      });

      await expect(loadSVGFromUrl('nonexistent.svg')).rejects.toThrow('Failed to fetch SVG: 404 Not Found');
    });

    it('should throw error when fetch throws', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(loadSVGFromUrl('test.svg')).rejects.toThrow('Network error');
    });
  });
});
